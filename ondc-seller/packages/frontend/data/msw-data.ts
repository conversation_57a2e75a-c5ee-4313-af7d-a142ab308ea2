/**
 * MSW Mock Data
 *
 * This module contains mock data used by MSW for API responses.
 */

export interface MockProduct {
  id: string;
  title: string;
  description: string;
  price: number;
  thumbnail: string;
  handle: string;
  vendor?: string;
  product_type?: string;
  tags: string[];
  images: Array<{
    id: string;
    src: string;
    alt?: string;
  }>;
  variants: Array<{
    id: string;
    title: string;
    price: number;
    available: boolean;
    inventory_quantity?: number;
  }>;
  category?: string;
  rating?: number;
  reviews?: number;
  created_at?: string;
  updated_at?: string;
}

export interface MockBanner {
  id: number;
  attributes: {
    title: string;
    description?: string;
    image?: {
      data?: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    link?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export interface MockOrder {
  id: string;
  order_number: string;
  customer: {
    name: string;
    email: string;
    phone?: string;
  };
  items: Array<{
    id: string;
    product_id: string;
    variant_id: string;
    title: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  created_at: string;
  updated_at: string;
}

// Mock Products Data - Comprehensive category coverage
export const mockProducts: MockProduct[] = [
  // Electronics Category
  {
    id: 'prod_1',
    title: 'Premium Wireless Headphones',
    description:
      'High-quality wireless headphones with active noise cancellation, 30-hour battery life, and premium sound quality.',
    price: 8999,
    thumbnail: 'https://picsum.photos/400/300?random=1',
    handle: 'premium-wireless-headphones',
    vendor: 'ONDC Seller',
    product_type: 'Electronics',
    category: 'electronics',
    tags: ['electronics', 'audio', 'wireless', 'noise-cancellation'],
    rating: 4.5,
    reviews: 324,
    images: [
      {
        id: 'prod_1-img-1',
        src: 'https://picsum.photos/600/600?random=1',
        alt: 'Premium Wireless Headphones',
      },
      {
        id: 'prod_1-img-2',
        src: 'https://picsum.photos/600/600?random=11',
        alt: 'Premium Wireless Headphones Side View',
      },
    ],
    variants: [
      {
        id: 'variant_1',
        title: 'Default Variant',
        price: 8999,
        available: true,
        inventory_quantity: 15,
      },
    ],
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-20T14:30:00Z',
  },
  {
    id: 'prod_2',
    title: 'Smart Fitness Watch',
    description:
      'Advanced fitness tracking watch with heart rate monitor, GPS, and 7-day battery life.',
    price: 12999,
    thumbnail: 'https://picsum.photos/400/300?random=2',
    handle: 'smart-fitness-watch',
    vendor: 'ONDC Seller',
    product_type: 'Electronics',
    category: 'electronics',
    tags: ['electronics', 'fitness', 'smartwatch', 'health'],
    rating: 4.7,
    reviews: 567,
    images: [
      {
        id: 'prod_2-img-1',
        src: 'https://picsum.photos/600/600?random=2',
        alt: 'Smart Fitness Watch',
      },
    ],
    variants: [
      {
        id: 'variant_2',
        title: 'Black Sport Band',
        price: 12999,
        available: true,
        inventory_quantity: 8,
      },
    ],
    created_at: '2024-01-12T12:00:00Z',
    updated_at: '2024-01-22T10:15:00Z',
  },
  {
    id: 'prod_3',
    title: 'Professional Camera Lens',
    description:
      'High-performance 85mm f/1.4 lens perfect for portrait photography with exceptional bokeh.',
    price: 45999,
    thumbnail: 'https://picsum.photos/400/300?random=3',
    handle: 'professional-camera-lens',
    vendor: 'ONDC Seller',
    product_type: 'Electronics',
    category: 'electronics',
    tags: ['electronics', 'photography', 'lens', 'professional'],
    rating: 4.9,
    reviews: 78,
    images: [
      {
        id: 'prod_3-img-1',
        src: 'https://picsum.photos/600/600?random=3',
        alt: 'Professional Camera Lens',
      },
    ],
    variants: [
      {
        id: 'variant_3',
        title: 'Canon Mount',
        price: 45999,
        available: true,
        inventory_quantity: 3,
      },
    ],
    created_at: '2024-01-08T09:00:00Z',
    updated_at: '2024-01-25T11:20:00Z',
  },
  {
    id: 'elec-4',
    title: 'Gaming Mechanical Keyboard',
    description:
      'RGB backlit mechanical keyboard with Cherry MX switches, perfect for gaming and productivity.',
    price: 3499,
    thumbnail: 'https://placehold.co/400x300/8B5CF6/FFFFFF?text=Keyboard',
    handle: 'gaming-mechanical-keyboard',
    vendor: 'GameGear',
    product_type: 'Electronics',
    category: 'electronics',
    tags: ['electronics', 'gaming', 'keyboard', 'mechanical'],
    rating: 4.6,
    reviews: 189,
    images: [
      {
        id: 'elec-4-img-1',
        src: 'https://placehold.co/600x600/8B5CF6/FFFFFF?text=Keyboard',
        alt: 'Gaming Mechanical Keyboard',
      },
    ],
    variants: [
      {
        id: 'elec-4-var-1',
        title: 'Cherry MX Red',
        price: 3499,
        available: true,
        inventory_quantity: 25,
      },
      {
        id: 'elec-4-var-2',
        title: 'Cherry MX Blue',
        price: 3499,
        available: true,
        inventory_quantity: 18,
      },
    ],
    created_at: '2024-01-18T14:00:00Z',
    updated_at: '2024-01-28T16:30:00Z',
  },

  // Fashion Category
  {
    id: 'fash-1',
    title: 'Organic Cotton T-Shirt',
    description:
      'Comfortable and sustainable organic cotton t-shirt made from 100% certified organic cotton.',
    price: 899,
    thumbnail: 'https://placehold.co/400x300/10B981/FFFFFF?text=T-Shirt',
    handle: 'organic-cotton-t-shirt',
    vendor: 'EcoWear',
    product_type: 'Clothing',
    category: 'fashion',
    tags: ['fashion', 'organic', 'sustainable', 'cotton'],
    rating: 4.3,
    reviews: 445,
    images: [
      {
        id: 'fash-1-img-1',
        src: 'https://placehold.co/600x600/10B981/FFFFFF?text=T-Shirt',
        alt: 'Organic Cotton T-Shirt',
      },
    ],
    variants: [
      {
        id: 'fash-1-var-1',
        title: 'Small - Blue',
        price: 899,
        available: true,
        inventory_quantity: 25,
      },
      {
        id: 'fash-1-var-2',
        title: 'Medium - Blue',
        price: 899,
        available: true,
        inventory_quantity: 40,
      },
      {
        id: 'fash-1-var-3',
        title: 'Large - Blue',
        price: 899,
        available: false,
        inventory_quantity: 0,
      },
      {
        id: 'fash-1-var-4',
        title: 'Small - Green',
        price: 899,
        available: true,
        inventory_quantity: 20,
      },
    ],
    created_at: '2024-01-10T08:00:00Z',
    updated_at: '2024-01-18T16:45:00Z',
  },
  {
    id: 'fash-2',
    title: 'Designer Denim Jeans',
    description:
      'Premium quality denim jeans with a modern slim fit and comfortable stretch fabric.',
    price: 2499,
    thumbnail: 'https://via.placeholder.com/400x300/1F2937/FFFFFF?text=Jeans',
    handle: 'designer-denim-jeans',
    vendor: 'DenimCo',
    product_type: 'Clothing',
    category: 'fashion',
    tags: ['fashion', 'denim', 'jeans', 'premium'],
    rating: 4.5,
    reviews: 298,
    images: [
      {
        id: 'fash-2-img-1',
        src: 'https://via.placeholder.com/600x600/1F2937/FFFFFF?text=Jeans',
        alt: 'Designer Denim Jeans',
      },
    ],
    variants: [
      {
        id: 'fash-2-var-1',
        title: '30x32 - Dark Blue',
        price: 2499,
        available: true,
        inventory_quantity: 15,
      },
      {
        id: 'fash-2-var-2',
        title: '32x32 - Dark Blue',
        price: 2499,
        available: true,
        inventory_quantity: 22,
      },
      {
        id: 'fash-2-var-3',
        title: '34x32 - Dark Blue',
        price: 2499,
        available: true,
        inventory_quantity: 18,
      },
    ],
    created_at: '2024-01-14T11:00:00Z',
    updated_at: '2024-01-24T13:15:00Z',
  },
  {
    id: 'fash-3',
    title: 'Luxury Silk Scarf',
    description:
      'Elegant silk scarf with hand-printed patterns, perfect for adding sophistication to any outfit.',
    price: 1899,
    thumbnail: 'https://via.placeholder.com/400x300/EC4899/FFFFFF?text=Silk+Scarf',
    handle: 'luxury-silk-scarf',
    vendor: 'SilkLux',
    product_type: 'Accessories',
    category: 'fashion',
    tags: ['fashion', 'silk', 'scarf', 'luxury', 'accessories'],
    rating: 4.8,
    reviews: 156,
    images: [
      {
        id: 'fash-3-img-1',
        src: 'https://via.placeholder.com/600x600/EC4899/FFFFFF?text=Silk+Scarf',
        alt: 'Luxury Silk Scarf',
      },
    ],
    variants: [
      {
        id: 'fash-3-var-1',
        title: 'Floral Pattern',
        price: 1899,
        available: true,
        inventory_quantity: 12,
      },
      {
        id: 'fash-3-var-2',
        title: 'Geometric Pattern',
        price: 1899,
        available: true,
        inventory_quantity: 8,
      },
    ],
    created_at: '2024-01-16T15:30:00Z',
    updated_at: '2024-01-26T17:45:00Z',
  },
  {
    id: 'fash-4',
    title: 'Leather Crossbody Bag',
    description:
      'Handcrafted genuine leather crossbody bag with multiple compartments and adjustable strap.',
    price: 3999,
    thumbnail: 'https://via.placeholder.com/400x300/92400E/FFFFFF?text=Leather+Bag',
    handle: 'leather-crossbody-bag',
    vendor: 'LeatherCraft',
    product_type: 'Accessories',
    category: 'fashion',
    tags: ['fashion', 'leather', 'bag', 'crossbody', 'handcrafted'],
    rating: 4.6,
    reviews: 234,
    images: [
      {
        id: 'fash-4-img-1',
        src: 'https://via.placeholder.com/600x600/92400E/FFFFFF?text=Leather+Bag',
        alt: 'Leather Crossbody Bag',
      },
    ],
    variants: [
      { id: 'fash-4-var-1', title: 'Brown', price: 3999, available: true, inventory_quantity: 20 },
      { id: 'fash-4-var-2', title: 'Black', price: 3999, available: true, inventory_quantity: 15 },
    ],
    created_at: '2024-01-20T10:00:00Z',
    updated_at: '2024-01-30T12:30:00Z',
  },

  // Home & Garden Category
  {
    id: 'home-1',
    title: 'Ergonomic Office Chair',
    description:
      'Premium ergonomic office chair with lumbar support, adjustable height, and breathable mesh back.',
    price: 4499,
    thumbnail: 'https://via.placeholder.com/400x300/059669/FFFFFF?text=Office+Chair',
    handle: 'ergonomic-office-chair',
    vendor: 'ComfortSeating',
    product_type: 'Furniture',
    category: 'home-garden',
    tags: ['home', 'furniture', 'office', 'ergonomic', 'chair'],
    rating: 4.6,
    reviews: 189,
    images: [
      {
        id: 'home-1-img-1',
        src: 'https://via.placeholder.com/600x600/059669/FFFFFF?text=Office+Chair',
        alt: 'Ergonomic Office Chair',
      },
    ],
    variants: [
      { id: 'home-1-var-1', title: 'Black', price: 4499, available: true, inventory_quantity: 25 },
      { id: 'home-1-var-2', title: 'Gray', price: 4499, available: true, inventory_quantity: 18 },
    ],
    created_at: '2024-01-11T09:30:00Z',
    updated_at: '2024-01-21T11:45:00Z',
  },
  {
    id: 'home-2',
    title: 'Premium Coffee Maker',
    description:
      'Professional-grade coffee maker with programmable settings, thermal carafe, and built-in grinder.',
    price: 1599,
    thumbnail: 'https://via.placeholder.com/400x300/7C2D12/FFFFFF?text=Coffee+Maker',
    handle: 'premium-coffee-maker',
    vendor: 'BrewMaster',
    product_type: 'Kitchen Appliances',
    category: 'home-garden',
    tags: ['home', 'kitchen', 'coffee', 'appliance'],
    rating: 4.4,
    reviews: 234,
    images: [
      {
        id: 'home-2-img-1',
        src: 'https://via.placeholder.com/600x600/7C2D12/FFFFFF?text=Coffee+Maker',
        alt: 'Premium Coffee Maker',
      },
    ],
    variants: [
      {
        id: 'home-2-var-1',
        title: 'Stainless Steel',
        price: 1599,
        available: true,
        inventory_quantity: 30,
      },
      { id: 'home-2-var-2', title: 'Black', price: 1599, available: true, inventory_quantity: 22 },
    ],
    created_at: '2024-01-13T14:20:00Z',
    updated_at: '2024-01-23T16:35:00Z',
  },
  {
    id: 'home-3',
    title: 'Smart LED Floor Lamp',
    description:
      'WiFi-enabled LED floor lamp with adjustable brightness, color temperature, and voice control.',
    price: 2299,
    thumbnail: 'https://via.placeholder.com/400x300/F59E0B/FFFFFF?text=Floor+Lamp',
    handle: 'smart-led-floor-lamp',
    vendor: 'SmartLighting',
    product_type: 'Lighting',
    category: 'home-garden',
    tags: ['home', 'lighting', 'smart', 'led', 'wifi'],
    rating: 4.7,
    reviews: 167,
    images: [
      {
        id: 'home-3-img-1',
        src: 'https://via.placeholder.com/600x600/F59E0B/FFFFFF?text=Floor+Lamp',
        alt: 'Smart LED Floor Lamp',
      },
    ],
    variants: [
      {
        id: 'home-3-var-1',
        title: 'White Base',
        price: 2299,
        available: true,
        inventory_quantity: 15,
      },
      {
        id: 'home-3-var-2',
        title: 'Black Base',
        price: 2299,
        available: true,
        inventory_quantity: 12,
      },
    ],
    created_at: '2024-01-17T12:15:00Z',
    updated_at: '2024-01-27T14:30:00Z',
  },
  {
    id: 'home-4',
    title: 'Bamboo Cutting Board Set',
    description:
      'Eco-friendly bamboo cutting board set with 3 different sizes and built-in compartments.',
    price: 799,
    thumbnail: 'https://via.placeholder.com/400x300/65A30D/FFFFFF?text=Cutting+Board',
    handle: 'bamboo-cutting-board-set',
    vendor: 'EcoKitchen',
    product_type: 'Kitchen Accessories',
    category: 'home-garden',
    tags: ['home', 'kitchen', 'bamboo', 'eco-friendly', 'cutting-board'],
    rating: 4.5,
    reviews: 312,
    images: [
      {
        id: 'home-4-img-1',
        src: 'https://via.placeholder.com/600x600/65A30D/FFFFFF?text=Cutting+Board',
        alt: 'Bamboo Cutting Board Set',
      },
    ],
    variants: [
      {
        id: 'home-4-var-1',
        title: '3-Piece Set',
        price: 799,
        available: true,
        inventory_quantity: 45,
      },
    ],
    created_at: '2024-01-19T10:45:00Z',
    updated_at: '2024-01-29T13:20:00Z',
  },

  // Sports & Fitness Category
  {
    id: 'sport-1',
    title: 'Professional Yoga Mat',
    description:
      'Non-slip yoga mat made from natural rubber with excellent grip and cushioning for all yoga practices.',
    price: 1299,
    thumbnail: 'https://via.placeholder.com/400x300/7C3AED/FFFFFF?text=Yoga+Mat',
    handle: 'professional-yoga-mat',
    vendor: 'YogaLife',
    product_type: 'Fitness Equipment',
    category: 'sports-fitness',
    tags: ['sports', 'fitness', 'yoga', 'mat', 'exercise'],
    rating: 4.8,
    reviews: 456,
    images: [
      {
        id: 'sport-1-img-1',
        src: 'https://via.placeholder.com/600x600/7C3AED/FFFFFF?text=Yoga+Mat',
        alt: 'Professional Yoga Mat',
      },
    ],
    variants: [
      {
        id: 'sport-1-var-1',
        title: 'Purple',
        price: 1299,
        available: true,
        inventory_quantity: 35,
      },
      { id: 'sport-1-var-2', title: 'Blue', price: 1299, available: true, inventory_quantity: 28 },
      { id: 'sport-1-var-3', title: 'Green', price: 1299, available: true, inventory_quantity: 32 },
    ],
    created_at: '2024-01-12T08:30:00Z',
    updated_at: '2024-01-22T10:45:00Z',
  },
  {
    id: 'sport-2',
    title: 'Adjustable Dumbbells Set',
    description:
      'Space-saving adjustable dumbbells with quick-change weight system, ranging from 5-50 lbs each.',
    price: 8999,
    thumbnail: 'https://via.placeholder.com/400x300/DC2626/FFFFFF?text=Dumbbells',
    handle: 'adjustable-dumbbells-set',
    vendor: 'FitGear',
    product_type: 'Fitness Equipment',
    category: 'sports-fitness',
    tags: ['sports', 'fitness', 'dumbbells', 'weights', 'strength'],
    rating: 4.6,
    reviews: 203,
    images: [
      {
        id: 'sport-2-img-1',
        src: 'https://via.placeholder.com/600x600/DC2626/FFFFFF?text=Dumbbells',
        alt: 'Adjustable Dumbbells Set',
      },
    ],
    variants: [
      {
        id: 'sport-2-var-1',
        title: '5-50 lbs Pair',
        price: 8999,
        available: true,
        inventory_quantity: 12,
      },
      {
        id: 'sport-2-var-2',
        title: '5-25 lbs Pair',
        price: 5999,
        available: true,
        inventory_quantity: 18,
      },
    ],
    created_at: '2024-01-15T11:20:00Z',
    updated_at: '2024-01-25T13:35:00Z',
  },
  {
    id: 'sport-3',
    title: 'Running Shoes - Performance',
    description:
      'Lightweight running shoes with advanced cushioning technology and breathable mesh upper.',
    price: 3499,
    thumbnail: 'https://via.placeholder.com/400x300/2563EB/FFFFFF?text=Running+Shoes',
    handle: 'running-shoes-performance',
    vendor: 'RunTech',
    product_type: 'Athletic Footwear',
    category: 'sports-fitness',
    tags: ['sports', 'running', 'shoes', 'athletic', 'performance'],
    rating: 4.7,
    reviews: 389,
    images: [
      {
        id: 'sport-3-img-1',
        src: 'https://via.placeholder.com/600x600/2563EB/FFFFFF?text=Running+Shoes',
        alt: 'Running Shoes - Performance',
      },
    ],
    variants: [
      {
        id: 'sport-3-var-1',
        title: 'Size 8 - Blue',
        price: 3499,
        available: true,
        inventory_quantity: 15,
      },
      {
        id: 'sport-3-var-2',
        title: 'Size 9 - Blue',
        price: 3499,
        available: true,
        inventory_quantity: 22,
      },
      {
        id: 'sport-3-var-3',
        title: 'Size 10 - Blue',
        price: 3499,
        available: true,
        inventory_quantity: 18,
      },
      {
        id: 'sport-3-var-4',
        title: 'Size 8 - Black',
        price: 3499,
        available: true,
        inventory_quantity: 12,
      },
    ],
    created_at: '2024-01-18T09:15:00Z',
    updated_at: '2024-01-28T11:30:00Z',
  },
  {
    id: 'sport-4',
    title: 'Resistance Bands Set',
    description:
      'Complete resistance bands set with 5 different resistance levels, handles, and door anchor.',
    price: 999,
    thumbnail: 'https://via.placeholder.com/400x300/059669/FFFFFF?text=Resistance+Bands',
    handle: 'resistance-bands-set',
    vendor: 'FlexFit',
    product_type: 'Fitness Equipment',
    category: 'sports-fitness',
    tags: ['sports', 'fitness', 'resistance', 'bands', 'portable'],
    rating: 4.5,
    reviews: 278,
    images: [
      {
        id: 'sport-4-img-1',
        src: 'https://via.placeholder.com/600x600/059669/FFFFFF?text=Resistance+Bands',
        alt: 'Resistance Bands Set',
      },
    ],
    variants: [
      {
        id: 'sport-4-var-1',
        title: '5-Band Set',
        price: 999,
        available: true,
        inventory_quantity: 50,
      },
    ],
    created_at: '2024-01-21T13:40:00Z',
    updated_at: '2024-01-31T15:55:00Z',
  },

  // Beauty & Health Category
  {
    id: 'beauty-1',
    title: 'Vitamin C Serum',
    description:
      'Potent vitamin C serum with hyaluronic acid for brightening and anti-aging benefits.',
    price: 1299,
    thumbnail: 'https://via.placeholder.com/400x300/F97316/FFFFFF?text=Vitamin+C+Serum',
    handle: 'vitamin-c-serum',
    vendor: 'GlowSkin',
    product_type: 'Skincare',
    category: 'beauty-health',
    tags: ['beauty', 'skincare', 'vitamin-c', 'serum', 'anti-aging'],
    rating: 4.6,
    reviews: 342,
    images: [
      {
        id: 'beauty-1-img-1',
        src: 'https://via.placeholder.com/600x600/F97316/FFFFFF?text=Vitamin+C+Serum',
        alt: 'Vitamin C Serum',
      },
    ],
    variants: [
      { id: 'beauty-1-var-1', title: '30ml', price: 1299, available: true, inventory_quantity: 40 },
      { id: 'beauty-1-var-2', title: '60ml', price: 2199, available: true, inventory_quantity: 25 },
    ],
    created_at: '2024-01-14T16:20:00Z',
    updated_at: '2024-01-24T18:35:00Z',
  },
  {
    id: 'beauty-2',
    title: 'Natural Face Moisturizer',
    description: 'Organic face moisturizer with shea butter and jojoba oil for all skin types.',
    price: 899,
    thumbnail: 'https://via.placeholder.com/400x300/10B981/FFFFFF?text=Face+Moisturizer',
    handle: 'natural-face-moisturizer',
    vendor: 'PureNature',
    product_type: 'Skincare',
    category: 'beauty-health',
    tags: ['beauty', 'skincare', 'moisturizer', 'natural', 'organic'],
    rating: 4.4,
    reviews: 267,
    images: [
      {
        id: 'beauty-2-img-1',
        src: 'https://via.placeholder.com/600x600/10B981/FFFFFF?text=Face+Moisturizer',
        alt: 'Natural Face Moisturizer',
      },
    ],
    variants: [
      { id: 'beauty-2-var-1', title: '50ml', price: 899, available: true, inventory_quantity: 35 },
    ],
    created_at: '2024-01-16T11:45:00Z',
    updated_at: '2024-01-26T13:20:00Z',
  },
  {
    id: 'beauty-3',
    title: 'Protein Powder - Vanilla',
    description:
      'High-quality whey protein powder with 25g protein per serving, perfect for muscle building.',
    price: 2499,
    thumbnail: 'https://via.placeholder.com/400x300/7C3AED/FFFFFF?text=Protein+Powder',
    handle: 'protein-powder-vanilla',
    vendor: 'FitNutrition',
    product_type: 'Supplements',
    category: 'beauty-health',
    tags: ['health', 'fitness', 'protein', 'supplements', 'nutrition'],
    rating: 4.7,
    reviews: 189,
    images: [
      {
        id: 'beauty-3-img-1',
        src: 'https://via.placeholder.com/600x600/7C3AED/FFFFFF?text=Protein+Powder',
        alt: 'Protein Powder - Vanilla',
      },
    ],
    variants: [
      {
        id: 'beauty-3-var-1',
        title: '2 lbs - Vanilla',
        price: 2499,
        available: true,
        inventory_quantity: 28,
      },
      {
        id: 'beauty-3-var-2',
        title: '5 lbs - Vanilla',
        price: 4999,
        available: true,
        inventory_quantity: 15,
      },
    ],
    created_at: '2024-01-18T14:30:00Z',
    updated_at: '2024-01-28T16:45:00Z',
  },
  {
    id: 'beauty-4',
    title: 'Essential Oils Set',
    description:
      'Premium essential oils set with lavender, eucalyptus, and tea tree oils for aromatherapy.',
    price: 1599,
    thumbnail: 'https://via.placeholder.com/400x300/EC4899/FFFFFF?text=Essential+Oils',
    handle: 'essential-oils-set',
    vendor: 'AromaTherapy',
    product_type: 'Wellness',
    category: 'beauty-health',
    tags: ['beauty', 'wellness', 'essential-oils', 'aromatherapy', 'natural'],
    rating: 4.8,
    reviews: 156,
    images: [
      {
        id: 'beauty-4-img-1',
        src: 'https://via.placeholder.com/600x600/EC4899/FFFFFF?text=Essential+Oils',
        alt: 'Essential Oils Set',
      },
    ],
    variants: [
      {
        id: 'beauty-4-var-1',
        title: '3-Oil Set',
        price: 1599,
        available: true,
        inventory_quantity: 22,
      },
      {
        id: 'beauty-4-var-2',
        title: '6-Oil Set',
        price: 2999,
        available: true,
        inventory_quantity: 12,
      },
    ],
    created_at: '2024-01-20T12:15:00Z',
    updated_at: '2024-01-30T14:30:00Z',
  },

  // Books & Media Category
  {
    id: 'book-1',
    title: 'The Art of Programming',
    description:
      'Comprehensive guide to modern programming techniques and best practices for developers.',
    price: 1899,
    thumbnail: 'https://via.placeholder.com/400x300/1F2937/FFFFFF?text=Programming+Book',
    handle: 'the-art-of-programming',
    vendor: 'TechBooks',
    product_type: 'Books',
    category: 'books-media',
    tags: ['books', 'programming', 'technology', 'education', 'development'],
    rating: 4.8,
    reviews: 234,
    images: [
      {
        id: 'book-1-img-1',
        src: 'https://via.placeholder.com/600x600/1F2937/FFFFFF?text=Programming+Book',
        alt: 'The Art of Programming',
      },
    ],
    variants: [
      {
        id: 'book-1-var-1',
        title: 'Paperback',
        price: 1899,
        available: true,
        inventory_quantity: 45,
      },
      {
        id: 'book-1-var-2',
        title: 'Hardcover',
        price: 2899,
        available: true,
        inventory_quantity: 20,
      },
    ],
    created_at: '2024-01-13T10:20:00Z',
    updated_at: '2024-01-23T12:35:00Z',
  },
  {
    id: 'book-2',
    title: 'Mindfulness Meditation Guide',
    description:
      'Complete guide to mindfulness meditation with practical exercises and techniques.',
    price: 1299,
    thumbnail: 'https://via.placeholder.com/400x300/059669/FFFFFF?text=Meditation+Book',
    handle: 'mindfulness-meditation-guide',
    vendor: 'WellnessBooks',
    product_type: 'Books',
    category: 'books-media',
    tags: ['books', 'meditation', 'mindfulness', 'wellness', 'self-help'],
    rating: 4.6,
    reviews: 178,
    images: [
      {
        id: 'book-2-img-1',
        src: 'https://via.placeholder.com/600x600/059669/FFFFFF?text=Meditation+Book',
        alt: 'Mindfulness Meditation Guide',
      },
    ],
    variants: [
      {
        id: 'book-2-var-1',
        title: 'Paperback',
        price: 1299,
        available: true,
        inventory_quantity: 35,
      },
    ],
    created_at: '2024-01-17T09:45:00Z',
    updated_at: '2024-01-27T11:20:00Z',
  },
  {
    id: 'book-3',
    title: 'Wireless Bluetooth Earbuds',
    description: 'Premium wireless earbuds with noise cancellation and 24-hour battery life.',
    price: 3999,
    thumbnail: 'https://via.placeholder.com/400x300/3B82F6/FFFFFF?text=Earbuds',
    handle: 'wireless-bluetooth-earbuds',
    vendor: 'AudioTech',
    product_type: 'Audio',
    category: 'books-media',
    tags: ['audio', 'wireless', 'earbuds', 'bluetooth', 'music'],
    rating: 4.5,
    reviews: 289,
    images: [
      {
        id: 'book-3-img-1',
        src: 'https://via.placeholder.com/600x600/3B82F6/FFFFFF?text=Earbuds',
        alt: 'Wireless Bluetooth Earbuds',
      },
    ],
    variants: [
      { id: 'book-3-var-1', title: 'Black', price: 3999, available: true, inventory_quantity: 30 },
      { id: 'book-3-var-2', title: 'White', price: 3999, available: true, inventory_quantity: 25 },
    ],
    created_at: '2024-01-19T15:30:00Z',
    updated_at: '2024-01-29T17:45:00Z',
  },
  {
    id: 'book-4',
    title: 'Classic Literature Collection',
    description:
      'Beautiful hardcover collection of 10 classic literature books in a decorative box set.',
    price: 4999,
    thumbnail: 'https://via.placeholder.com/400x300/92400E/FFFFFF?text=Classic+Books',
    handle: 'classic-literature-collection',
    vendor: 'ClassicReads',
    product_type: 'Books',
    category: 'books-media',
    tags: ['books', 'literature', 'classic', 'collection', 'hardcover'],
    rating: 4.9,
    reviews: 145,
    images: [
      {
        id: 'book-4-img-1',
        src: 'https://via.placeholder.com/600x600/92400E/FFFFFF?text=Classic+Books',
        alt: 'Classic Literature Collection',
      },
    ],
    variants: [
      {
        id: 'book-4-var-1',
        title: '10-Book Set',
        price: 4999,
        available: true,
        inventory_quantity: 18,
      },
    ],
    created_at: '2024-01-22T11:10:00Z',
    updated_at: '2024-02-01T13:25:00Z',
  },
];

// Mock Banners Data
export const mockBanners: MockBanner[] = [
  {
    id: 1,
    attributes: {
      title: 'Welcome to ONDC Seller Platform',
      description:
        "Start selling on India's Open Network for Digital Commerce and reach millions of customers.",
      link: '/dashboard',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T12:00:00Z',
    },
  },
  {
    id: 2,
    attributes: {
      title: 'Boost Your Sales with Analytics',
      description:
        'Use our advanced analytics tools to understand your customers and optimize your sales.',
      link: '/analytics',
      isActive: true,
      createdAt: '2024-01-05T00:00:00Z',
      updatedAt: '2024-01-20T14:30:00Z',
    },
  },
  {
    id: 3,
    attributes: {
      title: 'New Product Management Features',
      description:
        'Manage your inventory more efficiently with our latest product management tools.',
      link: '/products',
      isActive: true,
      createdAt: '2024-01-10T00:00:00Z',
      updatedAt: '2024-01-25T09:45:00Z',
    },
  },
];

// Mock Orders Data
export const mockOrders: MockOrder[] = [
  {
    id: 'order-1',
    order_number: 'ORD-2024-001',
    customer: {
      name: 'Rajesh Kumar',
      email: '<EMAIL>',
      phone: '+91 9876543210',
    },
    items: [
      {
        id: 'item-1',
        product_id: '1',
        variant_id: 'variant-1',
        title: 'Premium Wireless Headphones - Black',
        quantity: 1,
        price: 2999,
        total: 2999,
      },
    ],
    subtotal: 2999,
    tax: 539.82,
    shipping: 0,
    total: 3538.82,
    status: 'confirmed',
    created_at: '2024-01-20T10:30:00Z',
    updated_at: '2024-01-20T11:00:00Z',
  },
  {
    id: 'order-2',
    order_number: 'ORD-2024-002',
    customer: {
      name: 'Priya Sharma',
      email: '<EMAIL>',
      phone: '+91 9876543211',
    },
    items: [
      {
        id: 'item-2',
        product_id: '2',
        variant_id: 'variant-4',
        title: 'Organic Cotton T-Shirt - Small Blue',
        quantity: 2,
        price: 899,
        total: 1798,
      },
    ],
    subtotal: 1798,
    tax: 323.64,
    shipping: 50,
    total: 2171.64,
    status: 'shipped',
    created_at: '2024-01-19T14:15:00Z',
    updated_at: '2024-01-21T09:30:00Z',
  },
];

// Helper functions
export function getProductById(id: string): MockProduct | undefined {
  return mockProducts.find(product => product.id === id);
}

export function getProductByHandle(handle: string): MockProduct | undefined {
  return mockProducts.find(product => product.handle === handle);
}

export function getProductsByCategory(category: string): MockProduct[] {
  return mockProducts.filter(product => product.category?.toLowerCase() === category.toLowerCase());
}

export function searchProducts(query: string): MockProduct[] {
  const searchTerm = query.toLowerCase();
  return mockProducts.filter(
    product =>
      product.title.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

export function getBannerById(id: number): MockBanner | undefined {
  return mockBanners.find(banner => banner.id === id);
}

export function getActiveBanners(): MockBanner[] {
  return mockBanners.filter(banner => banner.attributes.isActive);
}

export function getOrderById(id: string): MockOrder | undefined {
  return mockOrders.find(order => order.id === id);
}

export function getOrdersByStatus(status: MockOrder['status']): MockOrder[] {
  return mockOrders.filter(order => order.status === status);
}

// Pagination helper
export function paginateResults<T>(
  items: T[],
  page: number = 1,
  limit: number = 10
): {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
} {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedItems = items.slice(startIndex, endIndex);
  const totalPages = Math.ceil(items.length / limit);

  return {
    data: paginatedItems,
    pagination: {
      page,
      limit,
      total: items.length,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

// Additional exports for compatibility
export const MOCK_PRODUCTS = mockProducts;

export function generateCategoryProducts(category: string, count: number = 10): MockProduct[] {
  return Array.from({ length: count }, (_, i) => ({
    id: `${category}-${i + 1}`,
    title: `${category} Product ${i + 1}`,
    description: `A great ${category} product for your needs`,
    price: Math.floor(Math.random() * 5000) + 500,
    thumbnail: `https://via.placeholder.com/400x300/3B82F6/FFFFFF?text=${encodeURIComponent(category)}+${i + 1}`,
    handle: `${category}-product-${i + 1}`,
    vendor: 'Mock Vendor',
    product_type: category,
    category: category.toLowerCase(),
    tags: [category.toLowerCase(), 'mock', 'product'],
    rating: Math.round((Math.random() * 2 + 3) * 10) / 10,
    reviews: Math.floor(Math.random() * 200) + 10,
    images: [
      {
        id: `${category}-${i + 1}-img-1`,
        src: `https://via.placeholder.com/600x600/3B82F6/FFFFFF?text=${encodeURIComponent(category)}+${i + 1}`,
        alt: `${category} Product ${i + 1}`,
      },
    ],
    variants: [
      {
        id: `${category}-${i + 1}-variant-1`,
        title: 'Default',
        price: Math.floor(Math.random() * 5000) + 500,
        available: Math.random() > 0.2,
        inventory_quantity: Math.floor(Math.random() * 100) + 1,
      },
    ],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }));
}

export function getFilteredProducts(filters: {
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
}): MockProduct[] {
  let filtered = [...mockProducts];

  if (filters.category) {
    filtered = filtered.filter(p => p.category === filters.category);
  }

  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filtered = filtered.filter(
      p =>
        p.title.toLowerCase().includes(searchTerm) ||
        p.description.toLowerCase().includes(searchTerm) ||
        p.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  if (filters.minPrice) {
    filtered = filtered.filter(p => p.price >= filters.minPrice!);
  }

  if (filters.maxPrice) {
    filtered = filtered.filter(p => p.price <= filters.maxPrice!);
  }

  return filtered;
}

export function createMockCart(): any {
  return {
    id: `cart_${Date.now()}`,
    items: [],
    subtotal: 0,
    total: 0,
    item_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
}

export function createMockCartItem(
  productId: string,
  variantId: string,
  quantity: number = 1
): any {
  const product = getProductById(productId);
  const variant = product?.variants.find(v => v.id === variantId);

  if (!product || !variant) {
    throw new Error('Product or variant not found');
  }

  return {
    id: `item_${Date.now()}`,
    variant_id: variantId,
    product_id: productId,
    title: `${product.title} - ${variant.title}`,
    price: variant.price,
    quantity,
    total: variant.price * quantity,
    image: product.thumbnail,
    handle: product.handle,
  };
}

// Category metadata for enhanced category pages
export const categoryMetadata = {
  electronics: {
    name: 'Electronics',
    description: 'Latest gadgets, devices, and electronic accessories',
    image: '/images/categories/electronics.jpg',
    color: '#3B82F6',
    icon: '📱',
  },
  fashion: {
    name: 'Fashion',
    description: 'Trendy clothing, accessories, and style essentials',
    image: '/images/categories/fashion.jpg',
    color: '#EC4899',
    icon: '👗',
  },
  'home-garden': {
    name: 'Home & Garden',
    description: 'Furniture, appliances, and home improvement items',
    image: '/images/categories/home-garden.jpg',
    color: '#059669',
    icon: '🏠',
  },
  'sports-fitness': {
    name: 'Sports & Fitness',
    description: 'Athletic gear, fitness equipment, and sports accessories',
    image: '/images/categories/sports.jpg',
    color: '#DC2626',
    icon: '⚽',
  },
  'books-media': {
    name: 'Books & Media',
    description: 'Books, audiobooks, and digital media content',
    image: '/images/categories/books.jpg',
    color: '#1F2937',
    icon: '📚',
  },
  'beauty-health': {
    name: 'Beauty & Health',
    description: 'Skincare, wellness products, and health supplements',
    image: '/images/categories/beauty.jpg',
    color: '#F97316',
    icon: '💄',
  },
};

// Get category metadata by slug
export function getCategoryMetadata(slug: string) {
  return categoryMetadata[slug as keyof typeof categoryMetadata];
}
