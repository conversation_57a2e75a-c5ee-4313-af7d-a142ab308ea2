'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  thumbnail: string;
  category: string;
  rating: number;
  sales: number;
}

const FeaturedProducts: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock featured products data
    const mockProducts: Product[] = [
      {
        id: '1',
        title: 'Premium Wireless Headphones',
        description: 'High-quality wireless headphones with noise cancellation',
        price: 2999,
        thumbnail: 'https://placehold.co/300x300/3B82F6/FFFFFF?text=Headphones',
        category: 'Electronics',
        rating: 4.5,
        sales: 1250,
      },
      {
        id: '2',
        title: 'Organic Cotton T-Shirt',
        description: 'Comfortable and sustainable organic cotton t-shirt',
        price: 899,
        thumbnail: 'https://placehold.co/300x300/10B981/FFFFFF?text=T-Shirt',
        category: 'Fashion',
        rating: 4.3,
        sales: 890,
      },
      {
        id: '3',
        title: 'Smart Fitness Watch',
        description: 'Advanced fitness tracking with heart rate monitor',
        price: 4999,
        thumbnail: 'https://placehold.co/300x300/F59E0B/FFFFFF?text=Watch',
        category: 'Electronics',
        rating: 4.7,
        sales: 2100,
      },
      {
        id: '4',
        title: 'Artisan Coffee Beans',
        description: 'Premium single-origin coffee beans, freshly roasted',
        price: 599,
        thumbnail: 'https://placehold.co/300x300/8B5CF6/FFFFFF?text=Coffee',
        category: 'Food & Beverages',
        rating: 4.8,
        sales: 750,
      },
      {
        id: '5',
        title: 'Yoga Mat Pro',
        description: 'Non-slip premium yoga mat for all fitness levels',
        price: 1299,
        thumbnail: 'https://placehold.co/300x300/EF4444/FFFFFF?text=Yoga+Mat',
        category: 'Sports',
        rating: 4.4,
        sales: 650,
      },
      {
        id: '6',
        title: 'Ceramic Plant Pot',
        description: 'Handcrafted ceramic pot perfect for indoor plants',
        price: 799,
        thumbnail: 'https://placehold.co/300x300/06B6D4/FFFFFF?text=Plant+Pot',
        category: 'Home & Garden',
        rating: 4.6,
        sales: 420,
      },
    ];

    // Simulate API call
    setTimeout(() => {
      setProducts(mockProducts);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Featured Products</h2>
            <p className="mt-4 text-lg text-gray-600">Discover our top-selling products</p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse"
              >
                <div className="h-64 bg-gray-300"></div>
                <div className="p-6">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
                  <div className="h-6 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900">Featured Products</h2>
          <p className="mt-4 text-lg text-gray-600">
            Discover our top-selling products across various categories
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map(product => (
            <div
              key={product.id}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
            >
              <div className="relative h-64">
                <Image src={product.thumbnail} alt={product.title} fill className="object-cover" />
                <div className="absolute top-4 right-4">
                  <span className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                    {product.category}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.title}</h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{product.description}</p>

                <div className="flex items-center justify-between mb-4">
                  <span className="text-2xl font-bold text-blue-600">
                    ₹{(product.price || 0).toLocaleString()}
                  </span>
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                      <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                    </svg>
                    <span className="text-sm text-gray-600">{product.rating}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">{product.sales} sold</span>
                  <Link
                    href={`/products/${product.id}`}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
                  >
                    View Details
                    <svg
                      className="w-4 h-4 ml-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            href="/products"
            className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            View All Products
            <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 8l4 4m0 0l-4 4m4-4H3"
              />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
