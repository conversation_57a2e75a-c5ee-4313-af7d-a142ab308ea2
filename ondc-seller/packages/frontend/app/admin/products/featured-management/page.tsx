'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  StarIcon,
  FireIcon,
  TrophyIcon,
  PlusIcon,
  XMarkIcon,
  EyeIcon,
  ClockIcon,
  CalendarIcon,
  CheckIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  Bars3Icon,
  PencilIcon,
  TrashIcon,
  DocumentDuplicateIcon,
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon, FireIcon as FireSolidIcon } from '@heroicons/react/24/solid';

interface Product {
  id: string;
  name: string;
  image: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  category: string;
  rating: number;
  reviews: number;
  inStock: boolean;
  featured: boolean;
  topSelling: boolean;
  hotDeal: boolean;
  position?: number;
}

interface FeaturedSection {
  id: string;
  title: string;
  description: string;
  products: Product[];
  maxProducts: number;
  isActive: boolean;
  lastUpdated: string;
}

interface ScheduledPromotion {
  id: string;
  productId: string;
  productName: string;
  type: 'featured' | 'hot-deal' | 'top-selling';
  startDate: string;
  endDate: string;
  discount?: number;
  isActive: boolean;
}

export default function FeaturedProductsManagementPage() {
  const [activeTab, setActiveTab] = useState<'featured' | 'top-selling' | 'hot-deals' | 'schedule'>(
    'featured'
  );
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [draggedItem, setDraggedItem] = useState<Product | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Real data from Strapi CMS
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);

  const [featuredSections, setFeaturedSections] = useState<FeaturedSection[]>([
    {
      id: 'featured-products',
      title: 'Featured Products',
      description: 'Handpicked products showcased on the homepage',
      products: [],
      maxProducts: 8,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: 'top-selling',
      title: 'Top Selling Products',
      description: 'Best performing products based on sales data',
      products: [],
      maxProducts: 6,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: 'hot-deals',
      title: 'Hot Deal Products',
      description: 'Limited-time offers and promotional products',
      products: [],
      maxProducts: 4,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
  ]);

  const [scheduledPromotions] = useState<ScheduledPromotion[]>([
    {
      id: 'promo-1',
      productId: 'prod-5',
      productName: 'Converse Chuck Taylor',
      type: 'featured',
      startDate: '2024-01-20T00:00:00Z',
      endDate: '2024-01-27T23:59:59Z',
      isActive: false,
    },
    {
      id: 'promo-2',
      productId: 'prod-4',
      productName: 'New Balance 990v5',
      type: 'hot-deal',
      startDate: '2024-01-18T00:00:00Z',
      endDate: '2024-01-25T23:59:59Z',
      discount: 15,
      isActive: false,
    },
  ]);

  // Fetch all products from Strapi CMS
  const fetchProducts = async () => {
    try {
      setIsLoadingProducts(true);
      setError(null);

      console.log('🚀 Fetching all products from Strapi CMS...');

      const response = await fetch('/api/admin/products/all?limit=100');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch products');
      }

      console.log('✅ Successfully fetched products:', data.products?.length || 0);

      setAllProducts(data.products || []);

      // Update featured sections with real data
      setFeaturedSections(sections =>
        sections.map(section => {
          let sectionProducts: Product[] = [];

          if (section.id === 'featured-products') {
            sectionProducts = (data.products || []).filter((p: Product) => p.featured);
          } else if (section.id === 'top-selling') {
            sectionProducts = (data.products || []).filter((p: Product) => p.topSelling);
          } else if (section.id === 'hot-deals') {
            sectionProducts = (data.products || []).filter((p: Product) => p.hotDeal);
          }

          return {
            ...section,
            products: sectionProducts.slice(0, section.maxProducts),
            lastUpdated: new Date().toISOString(),
          };
        })
      );
    } catch (error) {
      console.error('❌ Error fetching products:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch products');
    } finally {
      setIsLoadingProducts(false);
    }
  };

  useEffect(() => {
    fetchProducts();
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const getCurrentSection = () => {
    return featuredSections.find(section => {
      if (activeTab === 'featured') return section.id === 'featured-products';
      if (activeTab === 'top-selling') return section.id === 'top-selling';
      if (activeTab === 'hot-deals') return section.id === 'hot-deals';
      return false;
    });
  };

  const getAvailableProducts = () => {
    const currentSection = getCurrentSection();
    if (!currentSection) return [];

    return allProducts.filter(
      product =>
        !currentSection.products.some(p => p.id === product.id) &&
        product.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const handleDragStart = (product: Product) => {
    setDraggedItem(product);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    if (!draggedItem) return;

    const currentSection = getCurrentSection();
    if (!currentSection) return;

    const newProducts = [...currentSection.products];
    const draggedIndex = newProducts.findIndex(p => p.id === draggedItem.id);

    if (draggedIndex !== -1) {
      // Reordering within the list
      newProducts.splice(draggedIndex, 1);
      newProducts.splice(targetIndex, 0, draggedItem);
    } else {
      // Adding new product
      if (newProducts.length < currentSection.maxProducts) {
        newProducts.splice(targetIndex, 0, draggedItem);
      }
    }

    // Update the section
    setFeaturedSections(sections =>
      sections.map(section =>
        section.id === currentSection.id
          ? { ...section, products: newProducts, lastUpdated: new Date().toISOString() }
          : section
      )
    );

    setDraggedItem(null);
  };

  const addProductToSection = (product: Product) => {
    const currentSection = getCurrentSection();
    if (!currentSection || currentSection.products.length >= currentSection.maxProducts) return;

    setFeaturedSections(sections =>
      sections.map(section =>
        section.id === currentSection.id
          ? {
              ...section,
              products: [...section.products, product],
              lastUpdated: new Date().toISOString(),
            }
          : section
      )
    );
  };

  const removeProductFromSection = (productId: string) => {
    const currentSection = getCurrentSection();
    if (!currentSection) return;

    setFeaturedSections(sections =>
      sections.map(section =>
        section.id === currentSection.id
          ? {
              ...section,
              products: section.products.filter(p => p.id !== productId),
              lastUpdated: new Date().toISOString(),
            }
          : section
      )
    );
  };

  const moveProduct = (productId: string, direction: 'up' | 'down') => {
    const currentSection = getCurrentSection();
    if (!currentSection) return;

    const products = [...currentSection.products];
    const index = products.findIndex(p => p.id === productId);

    if (index === -1) return;
    if (direction === 'up' && index === 0) return;
    if (direction === 'down' && index === products.length - 1) return;

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    [products[index], products[newIndex]] = [products[newIndex], products[index]];

    setFeaturedSections(sections =>
      sections.map(section =>
        section.id === currentSection.id
          ? { ...section, products, lastUpdated: new Date().toISOString() }
          : section
      )
    );
  };

  const handleSaveChanges = async () => {
    try {
      setIsLoading(true);
      console.log('🚀 Saving featured products changes...');

      // Collect all products that need to be updated
      const productsToUpdate: { id: string; featured: boolean; section: string }[] = [];

      featuredSections.forEach(section => {
        const sectionType = section.id.replace('-products', '').replace('-', '_');

        // Mark products in this section as featured for their respective section
        section.products.forEach(product => {
          productsToUpdate.push({
            id: product.id,
            featured: section.id === 'featured-products' ? true : product.featured,
            section: sectionType,
          });
        });
      });

      // Also mark products not in featured section as not featured
      allProducts.forEach(product => {
        const isInFeaturedSection = featuredSections
          .find(s => s.id === 'featured-products')
          ?.products.some(p => p.id === product.id);

        if (!isInFeaturedSection && product.featured) {
          productsToUpdate.push({
            id: product.id,
            featured: false,
            section: 'featured',
          });
        }
      });

      // Update products in batches
      const response = await fetch('/api/admin/products/featured', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          products: productsToUpdate,
          section: activeTab,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to save changes');
      }

      console.log('✅ Successfully saved featured products changes');
      alert('Changes saved successfully!');

      // Refresh products data
      await fetchProducts();
    } catch (error) {
      console.error('❌ Error saving changes:', error);
      alert(`Error saving changes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePublishChanges = async () => {
    try {
      await handleSaveChanges();
      console.log('✅ Changes published to live site');
      alert('Changes published to live site!');
    } catch (error) {
      console.error('❌ Error publishing changes:', error);
      alert(
        `Error publishing changes: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  };

  if (isLoading || isLoadingProducts) {
    return (
      <div className="space-y-6 px-4 sm:px-6 md:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded w-full mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-500">Loading products from Strapi CMS...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6 px-4 sm:px-6 md:px-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XMarkIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Products</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={fetchProducts}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Featured Products Management
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Customize homepage product sections and manage promotional content
          </p>
        </div>
        <div className="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          <button
            onClick={() => setIsPreviewMode(!isPreviewMode)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EyeIcon className="-ml-1 mr-2 h-5 w-5" />
            {isPreviewMode ? 'Edit Mode' : 'Preview'}
          </button>
          <button
            onClick={handleSaveChanges}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Save Draft
          </button>
          <button
            onClick={handlePublishChanges}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <CheckIcon className="-ml-1 mr-2 h-5 w-5" />
            Publish
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4 sm:px-6 md:px-8">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'featured', name: 'Featured Products', icon: StarIcon },
              { id: 'top-selling', name: 'Top Selling', icon: TrophyIcon },
              { id: 'hot-deals', name: 'Hot Deals', icon: FireIcon },
              { id: 'schedule', name: 'Scheduled Promotions', icon: ClockIcon },
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                <tab.icon className="h-5 w-5" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="px-4 sm:px-6 md:px-8">
        {activeTab !== 'schedule' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Current Section Products */}
            <div className="lg:col-span-2">
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        {getCurrentSection()?.title}
                      </h3>
                      <p className="text-sm text-gray-500">{getCurrentSection()?.description}</p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {getCurrentSection()?.products.length} / {getCurrentSection()?.maxProducts}{' '}
                      products
                    </div>
                  </div>

                  {isPreviewMode ? (
                    /* Preview Mode */
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {getCurrentSection()?.products.map(product => (
                        <div
                          key={product.id}
                          className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                        >
                          <div className="aspect-w-1 aspect-h-1 mb-3">
                            <Image
                              src={product.image}
                              alt={product.name}
                              width={200}
                              height={200}
                              className="w-full h-48 object-cover rounded"
                              onError={e => {
                                const target = e.target as HTMLImageElement;
                                target.src = '/images/products/placeholder.svg';
                              }}
                            />
                          </div>
                          <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
                          <p className="text-sm text-gray-500">{product.category}</p>
                          <div className="flex items-center mt-2">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <StarSolidIcon
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < Math.floor(product.rating)
                                      ? 'text-yellow-400'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="ml-1 text-sm text-gray-500">({product.reviews})</span>
                          </div>
                          <div className="mt-2">
                            <span className="text-lg font-bold text-gray-900">
                              ₹{product.price.toLocaleString()}
                            </span>
                            {product.originalPrice && (
                              <span className="ml-2 text-sm text-gray-500 line-through">
                                ₹{product.originalPrice.toLocaleString()}
                              </span>
                            )}
                          </div>
                          {product.discount && (
                            <div className="mt-1">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                {product.discount}% OFF
                              </span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    /* Edit Mode */
                    <div className="space-y-3">
                      {getCurrentSection()?.products.map((product, index) => (
                        <div
                          key={product.id}
                          draggable
                          onDragStart={() => handleDragStart(product)}
                          onDragOver={handleDragOver}
                          onDrop={e => handleDrop(e, index)}
                          className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 cursor-move"
                        >
                          <Bars3Icon className="h-5 w-5 text-gray-400" />
                          <div className="flex-shrink-0">
                            <Image
                              src={product.image}
                              alt={product.name}
                              width={64}
                              height={64}
                              className="h-16 w-16 rounded-lg object-cover"
                              onError={e => {
                                const target = e.target as HTMLImageElement;
                                target.src = '/images/products/placeholder.svg';
                              }}
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {product.name}
                            </h4>
                            <p className="text-sm text-gray-500">{product.category}</p>
                            <div className="flex items-center mt-1">
                              <span className="text-sm font-medium text-gray-900">
                                ₹{product.price.toLocaleString()}
                              </span>
                              {product.originalPrice && (
                                <span className="ml-2 text-sm text-gray-500 line-through">
                                  ₹{product.originalPrice.toLocaleString()}
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => moveProduct(product.id, 'up')}
                              disabled={index === 0}
                              className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                            >
                              <ArrowUpIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => moveProduct(product.id, 'down')}
                              disabled={index === (getCurrentSection()?.products.length || 0) - 1}
                              className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                            >
                              <ArrowDownIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => removeProductFromSection(product.id)}
                              className="p-1 text-red-400 hover:text-red-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      ))}

                      {/* Drop zone for new products */}
                      {(getCurrentSection()?.products.length || 0) <
                        (getCurrentSection()?.maxProducts || 0) && (
                        <div
                          onDragOver={handleDragOver}
                          onDrop={e => handleDrop(e, getCurrentSection()?.products.length || 0)}
                          className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400"
                        >
                          <PlusIcon className="mx-auto h-8 w-8 text-gray-400" />
                          <p className="mt-2 text-sm text-gray-500">
                            Drag products here or select from available products
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Available Products Sidebar */}
            {!isPreviewMode && (
              <div className="lg:col-span-1">
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      Available Products
                    </h3>

                    {/* Search */}
                    <div className="mb-4">
                      <input
                        type="text"
                        placeholder="Search products..."
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                    </div>

                    {/* Product List */}
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {getAvailableProducts().map(product => (
                        <div
                          key={product.id}
                          draggable
                          onDragStart={() => handleDragStart(product)}
                          className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-move"
                        >
                          <Image
                            src={product.image}
                            alt={product.name}
                            width={48}
                            height={48}
                            className="h-12 w-12 rounded object-cover"
                            onError={e => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/images/products/placeholder.svg';
                            }}
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="text-sm font-medium text-gray-900 truncate">
                              {product.name}
                            </h4>
                            <p className="text-sm text-gray-500">
                              ₹{product.price.toLocaleString()}
                            </p>
                          </div>
                          <button
                            onClick={() => addProductToSection(product)}
                            disabled={
                              (getCurrentSection()?.products.length || 0) >=
                              (getCurrentSection()?.maxProducts || 0)
                            }
                            className="p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50"
                          >
                            <PlusIcon className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Scheduled Promotions Tab */}
        {activeTab === 'schedule' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Scheduled Promotions
                </h3>
                <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                  <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                  Schedule Promotion
                </button>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Schedule
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {scheduledPromotions.map(promotion => (
                      <tr key={promotion.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {promotion.productName}
                          </div>
                          <div className="text-sm text-gray-500">ID: {promotion.productId}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              promotion.type === 'featured'
                                ? 'bg-blue-100 text-blue-800'
                                : promotion.type === 'hot-deal'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-green-100 text-green-800'
                            }`}
                          >
                            {promotion.type === 'featured' && (
                              <StarSolidIcon className="w-3 h-3 mr-1" />
                            )}
                            {promotion.type === 'hot-deal' && (
                              <FireSolidIcon className="w-3 h-3 mr-1" />
                            )}
                            {promotion.type === 'top-selling' && (
                              <TrophyIcon className="w-3 h-3 mr-1" />
                            )}
                            {promotion.type.replace('-', ' ')}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>{new Date(promotion.startDate).toLocaleDateString()}</div>
                          <div>to {new Date(promotion.endDate).toLocaleDateString()}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              promotion.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {promotion.isActive ? 'Active' : 'Scheduled'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-900">
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
