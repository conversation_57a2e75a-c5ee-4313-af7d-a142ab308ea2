import { NextResponse } from 'next/server';
import { getProductBySlug } from '@/lib/strapi-api';

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    console.log('🚀 Product Detail API: Fetching product by slug:', slug);

    // Fetch product from Strapi CMS
    const product = await getProductBySlug(slug);

    if (!product) {
      console.log('⚠️ Product Detail API: Product not found for slug:', slug);
      return NextResponse.json(
        {
          success: false,
          error: 'Product not found',
          data: null,
          timestamp: new Date().toISOString(),
        },
        { status: 404 }
      );
    }

    console.log('✅ Product Detail API: Successfully fetched product');
    console.log('📊 Product:', product.name);

    return NextResponse.json({
      success: true,
      data: product,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Product Detail API: Error fetching product:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        data: null,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
