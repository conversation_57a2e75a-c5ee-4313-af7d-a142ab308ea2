import { NextRequest, NextResponse } from 'next/server';

// Internal Strapi request function
const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN;

async function strapiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<{ data: T }> {
  const url = `${STRAPI_URL}/api${endpoint}`;

  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Temporarily disable authentication - Strapi allows public access
  // if (API_TOKEN) {
  //   defaultHeaders['Authorization'] = `Bearer ${API_TOKEN}`;
  // }

  const config: RequestInit = {
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`Strapi API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Strapi API request failed:', error);
    throw error;
  }
}

// PUT /api/admin/products/featured - Update featured status of products in Strapi
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { productId, featured, section } = body;

    console.log('🚀 Admin Featured Products API: Updating featured status...');
    console.log('📊 Parameters:', { productId, featured, section });

    if (!productId) {
      return NextResponse.json(
        { success: false, error: 'Product ID is required' },
        { status: 400 }
      );
    }

    // Update product featured status in Strapi
    const updateData = {
      featured: featured,
    };

    // Add section-specific fields if needed
    if (section) {
      updateData[`${section}_product`] = featured;
    }

    const response = await strapiRequest(`/products/${productId}`, {
      method: 'PUT',
      body: JSON.stringify({ data: updateData }),
    });

    console.log('✅ Admin Featured Products API: Successfully updated product');

    return NextResponse.json({
      success: true,
      message: 'Product featured status updated successfully',
      product: response.data,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Admin Featured Products API: Error updating product:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/products/featured - Bulk update featured products
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { products, section } = body;

    console.log('🚀 Admin Featured Products API: Bulk updating featured products...');
    console.log('📊 Parameters:', { productsCount: products?.length, section });

    if (!products || !Array.isArray(products)) {
      return NextResponse.json(
        { success: false, error: 'Products array is required' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    // Update each product
    for (const product of products) {
      try {
        const updateData = {
          featured: product.featured,
        };

        // Add section-specific fields if needed
        if (section) {
          updateData[`${section}_product`] = product.featured;
        }

        const response = await strapiRequest(`/products/${product.id}`, {
          method: 'PUT',
          body: JSON.stringify({ data: updateData }),
        });

        results.push({
          id: product.id,
          success: true,
          data: response.data,
        });
      } catch (error) {
        console.error(`❌ Error updating product ${product.id}:`, error);
        errors.push({
          id: product.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    console.log(
      `✅ Admin Featured Products API: Updated ${results.length} products, ${errors.length} errors`
    );

    return NextResponse.json({
      success: true,
      message: `Updated ${results.length} products successfully`,
      results,
      errors,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Admin Featured Products API: Error in bulk update:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/products/featured - Get current featured products configuration
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section') || 'featured';

    console.log('🚀 Admin Featured Products API: Getting featured products configuration...');
    console.log('📊 Parameters:', { section });

    // This could be enhanced to store featured product configurations in Strapi
    // For now, we'll return the current featured products
    const response = await strapiRequest(
      '/products?filters[featured][$eq]=true&populate=categories,images'
    );

    const featuredProducts =
      response.data?.map(product => ({
        id: product.id.toString(),
        name: product.name || product.attributes?.name,
        section: 'featured', // Default section
        position: 1, // Could be enhanced with actual position data
        isActive: true,
      })) || [];

    console.log(
      `✅ Admin Featured Products API: Found ${featuredProducts.length} featured products`
    );

    return NextResponse.json({
      success: true,
      featuredProducts,
      count: featuredProducts.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Admin Featured Products API: Error getting featured products:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        featuredProducts: [],
        count: 0,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
