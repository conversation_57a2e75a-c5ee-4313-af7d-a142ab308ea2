/**
 * API Services Index
 * Central export point for all API services and utilities
 */

// Base API infrastructure
export * from './base';
export { APIClient, APIClientError } from '../api-client';

// Domain-specific API services
export * from './products';
export * from './orders';
export * from './customers';
export * from './collections';
export * from './analytics';

// Service instances (ready to use)
export { productsAPI, adminProductsAPI, productVariantsAPI } from './products';
export { ordersAPI, adminOrdersAPI } from './orders';
export { adminCustomersAPI, customerGroupsAPI } from './customers';
export { collectionsAPI, adminCollectionsAPI } from './collections';
export { analyticsAPI } from './analytics';

// API client factory
export { APIServiceFactory, APICache } from './base';

// Legacy API support (for backward compatibility)
import axios from 'axios';

// Configuration
const STRAPI_BASE_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337';
const MEDUSA_BASE_URL = process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9001';
const STRAPI_API_TOKEN =
  process.env.STRAPI_API_TOKEN ||
  'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

// Log configuration for debugging
console.log('API Configuration:', {
  STRAPI_BASE_URL,
  MEDUSA_BASE_URL,
  HAS_STRAPI_TOKEN: !!STRAPI_API_TOKEN,
});

// Create axios instances for both APIs
const strapiAPI = axios.create({
  baseURL: STRAPI_BASE_URL,
  headers: {
    Authorization: `Bearer ${STRAPI_API_TOKEN}`,
  },
});

// Get publishable API key from environment or use a default for development
const MEDUSA_PUBLISHABLE_KEY =
  process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || 'pk_01HQD0GMQXVK5XJ0XS0ZNPJ8T5';

const medusaAPI = axios.create({
  baseURL: MEDUSA_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-publishable-api-key': MEDUSA_PUBLISHABLE_KEY,
  },
});

/**
 * Add tenant ID to requests for multi-tenancy support
 * @param {string} tenantId - The tenant ID
 */
export const setTenantId = tenantId => {
  if (tenantId) {
    medusaAPI.defaults.headers['x-tenant-id'] = tenantId;
  } else {
    delete medusaAPI.defaults.headers['x-tenant-id'];
  }
};

/**
 * Normalize Strapi data structure to a simpler format
 * @param {Object} strapiData - The data from Strapi API
 * @returns {Object} Normalized data
 */
// const normalizeStrapiData = (strapiData) => {
//   if (!strapiData) return null;

//   // Handle single item
//   if (strapiData.data && !Array.isArray(strapiData.data)) {
//     // Extract attributes if they exist (Strapi v4 format)
//     const item = {
//       id: strapiData.data.id,
//       ...(strapiData.data.attributes || strapiData.data)
//     };

//     // Process image - handle both direct image object and populated image relation
//     if (item.image) {
//       // If image is a relation with data property (Strapi v4 format)
//       if (item.image.data) {
//         const imageData = item.image.data;
//         // Handle both single image and collection
//         if (Array.isArray(imageData) && imageData.length > 0) {
//           const firstImage = imageData[0];
//           item.image = {
//             id: firstImage.id,
//             ...(firstImage.attributes || firstImage)
//           };
//         } else if (imageData) {
//           item.image = {
//             id: imageData.id,
//             ...(imageData.attributes || imageData)
//           };
//         }
//       }

//       // Handle formats if present
//       if (item.image.formats) {
//         // Prefer larger formats if available
//         const format = item.image.formats.large || item.image.formats.medium ||
//                       item.image.formats.small || item.image.formats.thumbnail;
//         if (format && format.url) {
//           item.image.url = format.url;
//         }
//       }

//       // Transform image URLs to absolute URLs if they're relative
//       if (item.image.url) {
//         if (!item.image.url.startsWith('http')) {
//           // Handle different Strapi URL patterns
//           if (item.image.url.startsWith('/uploads')) {
//             item.image.url = `${STRAPI_BASE_URL}${item.image.url}`;
//           } else {
//             item.image.url = `${STRAPI_BASE_URL}/uploads/${item.image.url}`;
//           }
//         }
//       }
//     }

//     // Add CTA text and link for banners if not present
//     if (item.link && !item.cta_link) {
//       item.cta_link = item.link;
//       item.cta_text = item.cta_text || 'Shop Now';
//     }

//     return item;
//   }

//   // Handle collection
//   if (strapiData.data && Array.isArray(strapiData.data)) {
//     const items = strapiData.data.map(item => {
//       // Extract attributes if they exist (Strapi v4 format)
//       const normalizedItem = {
//         id: item.id,
//         ...(item.attributes || item)
//       };

//       // Process image - handle both direct image object and populated image relation
//       if (normalizedItem.image) {
//         // If image is a relation with data property (Strapi v4 format)
//         if (normalizedItem.image.data) {
//           const imageData = normalizedItem.image.data;
//           // Handle both single image and collection
//           if (Array.isArray(imageData) && imageData.length > 0) {
//             const firstImage = imageData[0];
//             normalizedItem.image = {
//               id: firstImage.id,
//               ...(firstImage.attributes || firstImage)
//             };
//           } else if (imageData) {
//             normalizedItem.image = {
//               id: imageData.id,
//               ...(imageData.attributes || imageData)
//             };
//           }
//         }

//         // Handle formats if present
//         if (normalizedItem.image.formats) {
//           // Prefer larger formats if available
//           const format = normalizedItem.image.formats.large || normalizedItem.image.formats.medium ||
//                         normalizedItem.image.formats.small || normalizedItem.image.formats.thumbnail;
//           if (format && format.url) {
//             normalizedItem.image.url = format.url;
//           }
//         }

//         // Transform image URLs to absolute URLs if they're relative
//         if (normalizedItem.image.url) {
//           if (!normalizedItem.image.url.startsWith('http')) {
//             // Handle different Strapi URL patterns
//             if (normalizedItem.image.url.startsWith('/uploads')) {
//               normalizedItem.image.url = `${STRAPI_BASE_URL}${normalizedItem.image.url}`;
//             } else {
//               normalizedItem.image.url = `${STRAPI_BASE_URL}/uploads/${normalizedItem.image.url}`;
//             }
//           }
//         }
//       }

//       // Add CTA text and link for banners if not present
//       if (normalizedItem.link && !normalizedItem.cta_link) {
//         normalizedItem.cta_link = normalizedItem.link;
//         normalizedItem.cta_text = normalizedItem.cta_text || 'Shop Now';
//       }

//       return normalizedItem;
//     });

//     return {
//       items,
//       pagination: strapiData.meta?.pagination
//     };
//   }

//   return strapiData;
// };

/**
 * Normalize Medusa data structure to a simpler format
 * @param {Object} medusaData - The data from Medusa API
 * @returns {Object} Normalized data
 */
const normalizeMedusaData = medusaData => {
  if (!medusaData) return null;

  // Medusa typically returns data in a more direct format
  // but we might need to normalize pagination or specific structures
  if (medusaData.products) {
    return {
      items: medusaData.products,
      pagination: {
        count: medusaData.count,
        offset: medusaData.offset,
        limit: medusaData.limit,
      },
    };
  }

  return medusaData;
};

// Strapi API Methods
export const strapiClient = {
  /**
   * Get content from Strapi
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Normalized data
   */
  async get(endpoint, params = {}) {
    try {
      // Reduced logging
      console.log(`Fetching from Strapi: ${endpoint}`);
      const response = await strapiAPI.get(`/api/${endpoint}`, { params });

      // Process the response data
      if (!response.data) return null;

      // Handle single item
      if (response.data.data && !Array.isArray(response.data.data)) {
        const item = {
          id: response.data.data.id,
          ...(response.data.data.attributes || response.data.data),
        };
        return item;
      }

      // Handle collection
      if (response.data.data && Array.isArray(response.data.data)) {
        const items = response.data.data.map(item => ({
          id: item.id,
          ...(item.attributes || item),
        }));

        return {
          items,
          pagination: response.data.meta?.pagination,
        };
      }

      return response.data;
    } catch (error) {
      console.error(`Error fetching from Strapi (${endpoint}):`, error);
      throw error;
    }
  },

  // /**
  //  * Get mock data for development
  //  * @param {string} endpoint - API endpoint
  //  * @returns {Object} Mock data
  //  */
  // getMockData(endpoint) {
  //   if (endpoint.includes('banners')) {
  //     return {
  //       items: [
  //         {
  //           id: 1,
  //           title: 'Summer Sale',
  //           subtitle: 'Up to 50% off on all products',
  //           image: {
  //             url: 'https://via.placeholder.com/1200x400?text=Summer+Sale'
  //           },
  //           cta_text: 'Shop Now',
  //           cta_link: '/products',
  //           active: true
  //         },
  //         {
  //           id: 2,
  //           title: 'New Arrivals',
  //           subtitle: 'Check out our latest products',
  //           image: {
  //             url: 'https://via.placeholder.com/1200x400?text=New+Arrivals'
  //           },
  //           cta_text: 'Explore',
  //           cta_link: '/products',
  //           active: true
  //         }
  //       ]
  //     };
  //   }

  //   if (endpoint.includes('sellers')) {
  //     return {
  //       items: [
  //         {
  //           id: 1,
  //           name: 'Organic Farms',
  //           description: 'Premium organic produce directly from farms',
  //           ondc_seller_id: 'ONDC-ORG-001'
  //         },
  //         {
  //           id: 2,
  //           name: 'Tech Gadgets',
  //           description: 'Latest technology gadgets and accessories',
  //           ondc_seller_id: 'ONDC-TECH-002'
  //         }
  //       ]
  //     };
  //   }

  //   if (endpoint.includes('pages')) {
  //     const slug = endpoint.match(/filters\[slug\]\[\$eq\]=([^&]+)/)?.[1];
  //     if (slug === 'about-us') {
  //       return {
  //         id: 1,
  //         title: 'About Us',
  //         content: '<h2>Welcome to ONDC Seller Platform</h2><p>We are a marketplace connecting buyers and sellers across India.</p>',
  //         featured_image: {
  //           url: 'https://via.placeholder.com/800x400?text=About+Us'
  //         }
  //       };
  //     }
  //   }

  //   return null;
  // },

  /**
   * Get a page by slug
   * @param {string} slug - Page slug
   * @returns {Promise<Object>} Page data
   */
  async getPage(slug) {
    return this.get(`pages?filters[slug][$eq]=${slug}&populate=*`);
  },

  /**
   * Get all active banners
   * @returns {Promise<Object>} Banners data
   */
  async getBanners() {
    try {
      const result = await this.get('banners?filters[active][$eq]=true&populate=image');

      // Process banner images for efficient rendering
      if (result && result.items && result.items.length > 0) {
        result.items = result.items.map(banner => {
          // Process image if it exists
          if (banner.image) {
            // Handle Strapi v4 format with data property
            if (banner.image.data) {
              const imageData = banner.image.data;
              // Handle both single image and collection
              if (Array.isArray(imageData) && imageData.length > 0) {
                const firstImage = imageData[0];
                banner.image = {
                  id: firstImage.id,
                  ...(firstImage.attributes || firstImage),
                };
              } else if (imageData) {
                banner.image = {
                  id: imageData.id,
                  ...(imageData.attributes || imageData),
                };
              }
            }

            // Handle formats if present
            if (banner.image.formats) {
              // Prefer larger formats if available
              const format =
                banner.image.formats.large ||
                banner.image.formats.medium ||
                banner.image.formats.small ||
                banner.image.formats.thumbnail;
              if (format && format.url) {
                banner.image.url = format.url;
              }
            }

            // Transform image URLs to absolute URLs if they're relative
            if (banner.image.url && !banner.image.url.startsWith('http')) {
              // Ensure URL starts with a slash
              const imageUrl = banner.image.url.startsWith('/')
                ? banner.image.url
                : `/${banner.image.url}`;
              banner.image.url = `${STRAPI_BASE_URL}${imageUrl}`;
            }
          }

          // Add CTA text and link for banners if not present
          if (banner.link && !banner.cta_link) {
            banner.cta_link = banner.link;
            banner.cta_text = banner.cta_text || 'Shop Now';
          }

          return banner;
        });
      }

      return result;
    } catch (error) {
      console.error('Error in getBanners:', error);
      throw error;
    }
  },

  /**
   * Get all sellers
   * @returns {Promise<Object>} Sellers data
   */
  async getSellers() {
    return this.get('sellers?populate=*');
  },
};

// Medusa API Methods
export const medusaClient = {
  /**
   * Get data from Medusa
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Normalized data
   */
  async get(endpoint, params = {}) {
    try {
      console.log(`Fetching from Medusa: ${endpoint}`, {
        params,
        baseURL: medusaAPI.defaults.baseURL,
        headers: {
          'x-publishable-api-key': MEDUSA_PUBLISHABLE_KEY ? 'present' : 'missing',
        },
      });

      const response = await medusaAPI.get(`/store/${endpoint}`, { params });
      console.log(`Medusa response for ${endpoint}:`, response.data);
      return normalizeMedusaData(response.data);
    } catch (error) {
      console.error(`Error fetching from Medusa (${endpoint}):`, error);

      // Log more detailed error information
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
        console.error('Response headers:', error.response.headers);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }

      throw error;
    }
  },

  // /**
  //  * Get mock data for development
  //  * @param {string} endpoint - API endpoint
  //  * @param {Object} params - Query parameters
  //  * @returns {Object} Mock data
  //  */
  // getMockData(endpoint, params = {}) {
  //   if (endpoint === 'products' || endpoint.startsWith('products?')) {
  //     return {
  //       items: [
  //         {
  //           id: 'prod_01',
  //           title: 'Smartphone X',
  //           description: 'Latest smartphone with advanced features',
  //           thumbnail: 'https://via.placeholder.com/300x300?text=Smartphone+X',
  //           variants: [{ prices: [{ amount: 79900 }] }]
  //         },
  //         {
  //           id: 'prod_02',
  //           title: 'Laptop Pro',
  //           description: 'High-performance laptop for professionals',
  //           thumbnail: 'https://via.placeholder.com/300x300?text=Laptop+Pro',
  //           variants: [{ prices: [{ amount: 129900 }] }]
  //         },
  //         {
  //           id: 'prod_03',
  //           title: 'Wireless Earbuds',
  //           description: 'Premium sound quality with noise cancellation',
  //           thumbnail: 'https://via.placeholder.com/300x300?text=Wireless+Earbuds',
  //           variants: [{ prices: [{ amount: 14900 }] }]
  //         }
  //       ],
  //       pagination: {
  //         count: 3,
  //         offset: 0,
  //         limit: 10
  //       }
  //     };
  //   }

  //   if (endpoint.startsWith('products/')) {
  //     const productId = endpoint.split('/')[1];
  //     return {
  //       id: productId,
  //       title: 'Product ' + productId,
  //       description: 'Detailed description for product ' + productId,
  //       thumbnail: `https://via.placeholder.com/500x500?text=Product+${productId}`,
  //       variants: [{ prices: [{ amount: 99900 }] }]
  //     };
  //   }

  //   return null;
  // },

  /**
   * Get all products
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Products data
   */
  async getProducts(params = {}) {
    console.log('=== Calling medusaClient.getProducts() with params:', params, '===');
    try {
      const result = await this.get('products', params);
      console.log('getProducts result:', result);
      return result;
    } catch (error) {
      console.error('Error in getProducts:', error);
      throw error;
    }
  },

  /**
   * Get a product by ID
   * @param {string} id - Product ID
   * @returns {Promise<Object>} Product data
   */
  async getProduct(id) {
    return this.get(`products/${id}`);
  },

  /**
   * Get all collections
   * @returns {Promise<Object>} Collections data
   */
  async getCollections() {
    return this.get('collections');
  },

  /**
   * Get a collection by ID
   * @param {string} id - Collection ID
   * @returns {Promise<Object>} Collection data
   */
  async getCollection(id) {
    return this.get(`collections/${id}`);
  },

  /**
   * Create a cart
   * @returns {Promise<Object>} Cart data
   */
  async createCart() {
    try {
      const response = await medusaAPI.post('/store/carts');
      return response.data;
    } catch (error) {
      console.error('Error creating cart:', error);
      throw error;
    }
  },

  /**
   * Get a cart by ID
   * @param {string} id - Cart ID
   * @returns {Promise<Object>} Cart data
   */
  async getCart(id) {
    return this.get(`carts/${id}`);
  },

  /**
   * Add item to cart
   * @param {string} cartId - Cart ID
   * @param {Object} item - Item to add
   * @returns {Promise<Object>} Updated cart
   */
  async addToCart(cartId, item) {
    try {
      const response = await medusaAPI.post(`/store/carts/${cartId}/line-items`, item);
      return response.data;
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  },
};

// Combined API for convenience
export const api = {
  strapi: strapiClient,
  medusa: medusaClient,
  setTenantId,
};

export default api;
