/**
 * Fix Product Category Assignments Script
 * 
 * This script assigns products to appropriate subcategories based on product names
 */

const axios = require('axios');

// Strapi API configuration
const STRAPI_URL = 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN || '';

// Create axios instance for Strapi API
const strapiAPI = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
    ...(API_TOKEN && { 'Authorization': `Bearer ${API_TOKEN}` })
  }
});

/**
 * Get all existing categories from Strapi
 */
async function getAllCategories() {
  try {
    console.log('📂 Fetching all categories from Strapi...');
    const response = await strapiAPI.get('/product-categories?pagination[pageSize]=100');
    const categories = response.data.data || [];
    console.log(`✅ Found ${categories.length} categories`);
    return categories;
  } catch (error) {
    console.error('❌ Error fetching categories:', error.message);
    throw error;
  }
}

/**
 * Get all existing products from Strapi
 */
async function getAllProducts() {
  try {
    console.log('📦 Fetching all products from Strapi...');
    const response = await strapiAPI.get('/products?pagination[pageSize]=100&populate=categories');
    const products = response.data.data || [];
    console.log(`✅ Found ${products.length} products`);
    return products;
  } catch (error) {
    console.error('❌ Error fetching products:', error.message);
    throw error;
  }
}

/**
 * Find category by name (case-insensitive, flexible matching)
 */
function findCategoryByName(categories, targetName) {
  return categories.find(cat => {
    const catName = cat.name.toLowerCase().trim();
    const target = targetName.toLowerCase().trim();
    
    // Exact match
    if (catName === target) return true;
    
    // Partial match for similar names
    if (catName.includes(target) || target.includes(catName)) return true;
    
    return false;
  });
}

/**
 * Assign products to appropriate subcategories based on product names
 */
async function assignProductsToSubcategories(products, categories) {
  console.log('\n📦 Starting product category assignment...');
  
  const productCategoryMapping = {
    // Electronics subcategories
    'smartphones': ['phone', 'smartphone', 'mobile', 'iphone', 'android'],
    'laptops & computers': ['laptop', 'computer', 'pc', 'desktop', 'macbook', 'mouse', 'keyboard'],
    'audio & headphones': ['headphone', 'earphone', 'speaker', 'audio', 'sound', 'music', 'bluetooth'],
    'gaming': ['game', 'gaming', 'console', 'playstation', 'xbox', 'nintendo', 'controller'],
    'cameras & photography': ['camera', 'photo', 'photography', 'lens', 'webcam'],
    
    // Fashion subcategories
    'men\'s clothing': ['men', 'shirt', 'pants', 'jeans', 'jacket', 'suit'],
    'women\'s clothing': ['women', 'dress', 'blouse', 'skirt', 'top', 'sweater'],
    'shoes & footwear': ['shoe', 'boot', 'sneaker', 'sandal', 'heel', 'footwear', 'running'],
    'accessories': ['bag', 'wallet', 'watch', 'jewelry', 'belt', 'scarf', 'crossbody'],
    'kids & baby': ['kids', 'baby', 'children', 'infant', 'toddler', 'pajama'],
    
    // Home & Garden subcategories
    'furniture': ['chair', 'table', 'sofa', 'bed', 'desk', 'furniture', 'ottoman'],
    'home decor': ['decor', 'decoration', 'art', 'frame', 'vase', 'candle', 'pillow', 'wall', 'canvas'],
    'kitchen & dining': ['kitchen', 'cooking', 'utensil', 'plate', 'cup', 'cookware', 'dining', 'coffee', 'cutting', 'ceramic', 'dinnerware'],
    'garden & outdoor': ['garden', 'plant', 'outdoor', 'patio', 'lawn', 'flower', 'seed', 'indoor plant'],
    'storage & organization': ['storage', 'organizer', 'box', 'container', 'shelf', 'basket'],
    
    // Health & Beauty subcategories
    'skincare & personal care': ['skincare', 'cream', 'lotion', 'soap', 'personal care'],
    'makeup & cosmetics': ['makeup', 'cosmetic', 'lipstick', 'foundation', 'mascara', 'beauty'],
    'hair care': ['hair', 'shampoo', 'conditioner', 'styling', 'haircare'],
    'health supplements': ['vitamin', 'supplement', 'protein', 'health', 'nutrition', 'medicine'],
    'wellness & fitness': ['fitness', 'wellness', 'yoga', 'exercise', 'gym', 'workout', 'watch', 'diffuser'],
    
    // Sports & Outdoors subcategories
    'fitness equipment': ['fitness', 'gym', 'exercise', 'weight', 'treadmill', 'equipment'],
    'team sports': ['football', 'basketball', 'soccer', 'cricket', 'tennis', 'sports'],
    'outdoor recreation': ['camping', 'hiking', 'outdoor', 'adventure', 'backpack', 'tent'],
    'winter sports': ['ski', 'snow', 'winter', 'ice', 'snowboard'],
    'water sports': ['swim', 'water', 'surf', 'diving', 'boat', 'fishing'],
    
    // Books & Media subcategories
    'books': ['book', 'novel', 'story', 'literature', 'reading'],
    'movies & tv': ['movie', 'film', 'tv', 'dvd', 'blu-ray', 'series'],
    'music': ['music', 'cd', 'album', 'song', 'audio'],
    'games & toys': ['game', 'toy', 'puzzle', 'board game', 'card game'],
    'educational': ['education', 'learning', 'textbook', 'course', 'study'],
    
    // Automotive subcategories
    'car accessories': ['car', 'auto', 'vehicle', 'accessory', 'interior', 'exterior'],
    'car parts': ['engine', 'brake', 'tire', 'battery', 'part', 'component'],
    'motorcycle': ['motorcycle', 'bike', 'motorbike', 'scooter', 'helmet'],
    'tools & equipment': ['tool', 'equipment', 'wrench', 'screwdriver', 'repair'],
    
    // Food & Beverages subcategories
    'fresh produce': ['fruit', 'vegetable', 'fresh', 'organic', 'produce', 'apple'],
    'pantry staples': ['rice', 'flour', 'oil', 'spice', 'grain', 'staple'],
    'beverages': ['drink', 'juice', 'coffee', 'tea', 'water', 'soda'],
    'snacks & confectionery': ['snack', 'candy', 'chocolate', 'biscuit', 'chips'],
    'gourmet & specialty': ['gourmet', 'specialty', 'premium', 'artisan', 'delicacy']
  };
  
  let assignmentCount = 0;
  let errorCount = 0;
  
  for (const product of products) {
    try {
      const productName = product.name.toLowerCase();
      
      let bestMatch = null;
      let bestScore = 0;
      
      // Find the best matching subcategory
      for (const [subcategoryName, keywords] of Object.entries(productCategoryMapping)) {
        let score = 0;
        
        for (const keyword of keywords) {
          if (productName.includes(keyword)) {
            score += keyword.length; // Longer keywords get higher scores
          }
        }
        
        if (score > bestScore) {
          bestScore = score;
          bestMatch = subcategoryName;
        }
      }
      
      if (bestMatch && bestScore > 0) {
        // Find the subcategory in our categories list
        const subcategory = findCategoryByName(categories, bestMatch);
        
        if (subcategory) {
          // Update product with the subcategory
          const productId = product.documentId || product.id;
          await strapiAPI.put(`/products/${productId}`, {
            data: {
              categories: [subcategory.documentId || subcategory.id]
            }
          });
          
          assignmentCount++;
          console.log(`  ✅ Assigned "${product.name}" → ${subcategory.name}`);
        } else {
          console.log(`  ⚠️ Subcategory "${bestMatch}" not found for: ${product.name}`);
        }
      } else {
        console.log(`  ⚠️ No suitable subcategory found for: ${product.name}`);
      }
    } catch (error) {
      errorCount++;
      console.error(`  ❌ Failed to assign product ${product.name}:`, error.message);
    }
  }
  
  console.log(`\n📊 Product Assignment Summary:`);
  console.log(`✅ Products assigned: ${assignmentCount}`);
  console.log(`❌ Assignment errors: ${errorCount}`);
  
  return { assignmentCount, errorCount };
}

/**
 * Main function to fix product assignments
 */
async function fixProductAssignments() {
  console.log('🚀 Starting product category assignment fix...');
  
  try {
    // Get all existing data
    const categories = await getAllCategories();
    const products = await getAllProducts();
    
    // Assign products to subcategories
    const result = await assignProductsToSubcategories(products, categories);
    
    console.log('\n🎉 Product assignment fix completed!');
    return result;
    
  } catch (error) {
    console.error('❌ Error fixing product assignments:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  fixProductAssignments()
    .then((result) => {
      console.log('✅ Script completed successfully');
      console.log('📊 Final Result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = {
  fixProductAssignments,
  assignProductsToSubcategories
};
