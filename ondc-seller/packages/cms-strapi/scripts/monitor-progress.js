/**
 * Real-time migration progress monitor
 * Run this script to continuously monitor the migration progress
 */

const axios = require('axios');

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';

let lastStatus = {};

async function checkProgress() {
  try {
    const response = await axios.get(`${STRAPI_URL}/api/pages`);
    const pages = response.data.data;

    const currentStatus = {
      total: pages.length,
      pages: pages.map(page => ({
        title: page.title,
        slug: page.slug,
        hasContent: page.content && page.content.length > 50,
        hasMetadata: !!(page.excerpt && page.metaTitle),
        status: page.status,
      })),
    };

    // Check if status changed
    const statusChanged = JSON.stringify(currentStatus) !== JSON.stringify(lastStatus);

    if (statusChanged) {
      console.clear();
      console.log('🔄 STRAPI MIGRATION PROGRESS - LIVE MONITOR');
      console.log('='.repeat(60));
      console.log(`📊 Total Pages: ${currentStatus.total}/6`);
      console.log(`⏰ Last Update: ${new Date().toLocaleTimeString()}\n`);

      currentStatus.pages.forEach(page => {
        const contentIcon = page.hasContent ? '✅' : '⚠️';
        const metaIcon = page.hasMetadata ? '✅' : '⚠️';
        const statusIcon = page.status === 'published' ? '🟢' : '🟡';

        console.log(`${statusIcon} ${page.title} (${page.slug})`);
        console.log(
          `   Content: ${contentIcon} | Metadata: ${metaIcon} | Status: ${page.status || 'draft'}`
        );
      });

      console.log('\n📋 REMAINING TASKS:');
      const expectedSlugs = ['about-us', 'contact', 'privacy-policy', 'terms', 'faq', 'help'];
      const existingSlugs = currentStatus.pages.map(p => p.slug);

      expectedSlugs.forEach(slug => {
        const exists = existingSlugs.includes(slug);
        if (!exists) {
          console.log(`   🔲 CREATE: ${slug}`);
        } else {
          const page = currentStatus.pages.find(p => p.slug === slug);
          if (!page.hasContent) console.log(`   🔲 ADD CONTENT: ${slug}`);
          if (!page.hasMetadata) console.log(`   🔲 ADD METADATA: ${slug}`);
          if (page.status !== 'published') console.log(`   🔲 PUBLISH: ${slug}`);
        }
      });

      if (currentStatus.total === 6) {
        const allComplete = currentStatus.pages.every(
          p => p.hasContent && p.hasMetadata && p.status === 'published'
        );

        if (allComplete) {
          console.log('\n🎉 MIGRATION COMPLETE! All pages are ready.');
          console.log('🔗 Test the pages at: http://localhost:3001');
          process.exit(0);
        }
      }

      lastStatus = currentStatus;
    }
  } catch (error) {
    console.error('❌ Error checking progress:', error.message);
  }
}

console.log('🚀 Starting live migration monitor...');
console.log('📝 Use the content from: STRAPI_CONTENT_REFERENCE.md');
console.log(
  '🌐 Strapi Admin: http://localhost:1339/admin/content-manager/collection-types/api::page.page\n'
);

// Check immediately, then every 5 seconds
checkProgress();
setInterval(checkProgress, 5000);
