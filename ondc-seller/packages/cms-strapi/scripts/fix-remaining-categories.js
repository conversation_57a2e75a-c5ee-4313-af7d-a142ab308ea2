/**
 * Fix Remaining Categories Script
 * 
 * This script sets all remaining categories with null isSubcategory to false (parent categories)
 */

const axios = require('axios');

// Strapi API configuration
const STRAPI_URL = 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN || '';

// Create axios instance for Strapi API
const strapiAPI = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
    ...(API_TOKEN && { 'Authorization': `Bearer ${API_TOKEN}` })
  }
});

/**
 * Get all existing categories from Strapi
 */
async function getAllCategories() {
  try {
    console.log('📂 Fetching all categories from Strapi...');
    const response = await strapiAPI.get('/product-categories?pagination[pageSize]=100');
    const categories = response.data.data || [];
    console.log(`✅ Found ${categories.length} categories`);
    return categories;
  } catch (error) {
    console.error('❌ Error fetching categories:', error.message);
    throw error;
  }
}

/**
 * Update category with isSubcategory flag
 */
async function updateCategory(category, updateData) {
  try {
    // Use documentId for Strapi v5
    const categoryId = category.documentId || category.id;
    const response = await strapiAPI.put(`/product-categories/${categoryId}`, {
      data: updateData
    });
    return response.data;
  } catch (error) {
    console.error(`❌ Error updating category ${categoryId}:`, error.message);
    throw error;
  }
}

/**
 * Fix all remaining categories
 */
async function fixRemainingCategories() {
  console.log('🚀 Starting to fix remaining categories...');
  
  try {
    // Get all existing categories
    const categories = await getAllCategories();
    
    if (categories.length === 0) {
      console.log('⚠️ No categories found.');
      return;
    }
    
    // Find categories with null or undefined isSubcategory
    const categoriesToFix = categories.filter(cat => 
      cat.isSubcategory === null || cat.isSubcategory === undefined
    );
    
    console.log(`\n📋 Found ${categoriesToFix.length} categories to fix`);
    
    if (categoriesToFix.length === 0) {
      console.log('✅ All categories already have isSubcategory set!');
      return;
    }
    
    // Update each category to be a parent category (isSubcategory: false)
    let successCount = 0;
    let errorCount = 0;
    
    for (const category of categoriesToFix) {
      try {
        await updateCategory(category, {
          isSubcategory: false,
          parent: null
        });
        console.log(`✅ Fixed: ${category.name} → Parent Category`);
        successCount++;
      } catch (error) {
        console.error(`❌ Failed to fix: ${category.name}`);
        errorCount++;
      }
    }
    
    // Summary report
    console.log('\n📊 FIX REMAINING CATEGORIES SUMMARY');
    console.log('=====================================');
    console.log(`✅ Categories fixed: ${successCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log(`📋 Total processed: ${categoriesToFix.length}`);
    
    console.log('\n🎉 Fix remaining categories completed!');
    
  } catch (error) {
    console.error('❌ Error fixing remaining categories:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  fixRemainingCategories()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = {
  fixRemainingCategories
};
