/**
 * Comprehensive Strapi CMS Data Population Script
 *
 * This script populates Strapi CMS with comprehensive e-commerce data:
 * - 8+ main categories with subcategories
 * - 10-15 products per category
 * - Proper relationships between categories and products
 * - Configures API permissions for public access
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const STRAPI_URL = 'http://localhost:1337';
const ADMIN_JWT_TOKEN = process.env.STRAPI_ADMIN_JWT || '';

// Admin API client
const adminAPI = axios.create({
  baseURL: `${STRAPI_URL}/admin`,
  headers: {
    Authorization: `Bearer ${ADMIN_JWT_TOKEN}`,
    'Content-Type': 'application/json',
  },
});

// Public API client
const publicAPI = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Comprehensive category data
const categoriesData = [
  {
    name: 'Electronics',
    slug: 'electronics',
    description:
      'Latest gadgets and tech devices including smartphones, laptops, audio equipment, and gaming accessories',
    featured: true,
    subcategories: [
      {
        name: 'Smartphones',
        slug: 'smartphones',
        description: 'Latest mobile phones and accessories',
      },
      {
        name: 'Laptops & Computers',
        slug: 'laptops-computers',
        description: 'Laptops, desktops, and computer accessories',
      },
      {
        name: 'Audio & Headphones',
        slug: 'audio-headphones',
        description: 'Headphones, speakers, and audio equipment',
      },
      {
        name: 'Gaming',
        slug: 'gaming',
        description: 'Gaming consoles, accessories, and peripherals',
      },
      {
        name: 'Cameras & Photography',
        slug: 'cameras-photography',
        description: 'Digital cameras, lenses, and photography equipment',
      },
    ],
  },
  {
    name: 'Fashion & Apparel',
    slug: 'fashion',
    description: 'Trendy clothing and accessories for men, women, and children',
    featured: true,
    subcategories: [
      {
        name: "Men's Clothing",
        slug: 'mens-clothing',
        description: 'Shirts, pants, suits, and casual wear for men',
      },
      {
        name: "Women's Clothing",
        slug: 'womens-clothing',
        description: 'Dresses, tops, bottoms, and formal wear for women',
      },
      {
        name: 'Shoes & Footwear',
        slug: 'shoes-footwear',
        description: 'Sneakers, boots, sandals, and formal shoes',
      },
      {
        name: 'Accessories',
        slug: 'accessories',
        description: 'Bags, jewelry, watches, and fashion accessories',
      },
      {
        name: 'Kids & Baby',
        slug: 'kids-baby',
        description: 'Clothing and accessories for children and babies',
      },
    ],
  },
  {
    name: 'Home & Garden',
    slug: 'home-garden',
    description: 'Furniture and home decor items to beautify your living space',
    featured: true,
    subcategories: [
      {
        name: 'Furniture',
        slug: 'furniture',
        description: 'Sofas, chairs, tables, and bedroom furniture',
      },
      {
        name: 'Home Decor',
        slug: 'home-decor',
        description: 'Wall art, lighting, rugs, and decorative items',
      },
      {
        name: 'Kitchen & Dining',
        slug: 'kitchen-dining',
        description: 'Cookware, appliances, and dining essentials',
      },
      {
        name: 'Garden & Outdoor',
        slug: 'garden-outdoor',
        description: 'Plants, garden tools, and outdoor furniture',
      },
      {
        name: 'Storage & Organization',
        slug: 'storage-organization',
        description: 'Storage solutions and organizational tools',
      },
    ],
  },
  {
    name: 'Health & Beauty',
    slug: 'beauty-health',
    description: 'Personal care and wellness products for a healthy lifestyle',
    featured: true,
    subcategories: [
      {
        name: 'Skincare',
        slug: 'skincare',
        description: 'Cleansers, moisturizers, and skincare treatments',
      },
      {
        name: 'Makeup & Cosmetics',
        slug: 'makeup-cosmetics',
        description: 'Foundation, lipstick, eyeshadow, and beauty tools',
      },
      {
        name: 'Hair Care',
        slug: 'hair-care',
        description: 'Shampoo, conditioner, styling products, and hair tools',
      },
      {
        name: 'Health Supplements',
        slug: 'health-supplements',
        description: 'Vitamins, minerals, and nutritional supplements',
      },
      {
        name: 'Personal Care',
        slug: 'personal-care',
        description: 'Bath products, oral care, and personal hygiene',
      },
    ],
  },
  {
    name: 'Sports & Outdoors',
    slug: 'sports-fitness',
    description: 'Athletic gear and equipment for sports and fitness activities',
    featured: true,
    subcategories: [
      {
        name: 'Fitness Equipment',
        slug: 'fitness-equipment',
        description: 'Weights, cardio machines, and workout gear',
      },
      {
        name: 'Sports Apparel',
        slug: 'sports-apparel',
        description: 'Athletic clothing and activewear',
      },
      {
        name: 'Outdoor Recreation',
        slug: 'outdoor-recreation',
        description: 'Camping, hiking, and outdoor adventure gear',
      },
      {
        name: 'Team Sports',
        slug: 'team-sports',
        description: 'Equipment for football, basketball, soccer, and more',
      },
      {
        name: 'Water Sports',
        slug: 'water-sports',
        description: 'Swimming, surfing, and water activity equipment',
      },
    ],
  },
  {
    name: 'Books & Media',
    slug: 'books-media',
    description: 'Books, magazines, and digital media for entertainment and education',
    featured: true,
    subcategories: [
      {
        name: 'Fiction Books',
        slug: 'fiction-books',
        description: 'Novels, short stories, and fictional literature',
      },
      {
        name: 'Non-Fiction',
        slug: 'non-fiction',
        description: 'Educational, self-help, and informational books',
      },
      {
        name: 'Digital Media',
        slug: 'digital-media',
        description: 'E-books, audiobooks, and digital content',
      },
      {
        name: 'Magazines & Journals',
        slug: 'magazines-journals',
        description: 'Periodicals and academic publications',
      },
      {
        name: 'Educational Materials',
        slug: 'educational-materials',
        description: 'Textbooks, workbooks, and learning resources',
      },
    ],
  },
  {
    name: 'Automotive',
    slug: 'automotive',
    description: 'Car accessories, parts, and automotive maintenance products',
    featured: false,
    subcategories: [
      {
        name: 'Car Accessories',
        slug: 'car-accessories',
        description: 'Interior and exterior car accessories',
      },
      {
        name: 'Car Parts',
        slug: 'car-parts',
        description: 'Replacement parts and automotive components',
      },
      {
        name: 'Car Care',
        slug: 'car-care',
        description: 'Cleaning products and maintenance supplies',
      },
      {
        name: 'Tools & Equipment',
        slug: 'tools-equipment',
        description: 'Automotive tools and diagnostic equipment',
      },
      {
        name: 'Tires & Wheels',
        slug: 'tires-wheels',
        description: 'Tires, rims, and wheel accessories',
      },
    ],
  },
  {
    name: 'Food & Beverages',
    slug: 'food-beverages',
    description: 'Fresh food, beverages, and gourmet products',
    featured: false,
    subcategories: [
      {
        name: 'Fresh Produce',
        slug: 'fresh-produce',
        description: 'Fruits, vegetables, and fresh ingredients',
      },
      { name: 'Beverages', slug: 'beverages', description: 'Soft drinks, juices, coffee, and tea' },
      {
        name: 'Snacks & Confectionery',
        slug: 'snacks-confectionery',
        description: 'Chips, candy, and sweet treats',
      },
      {
        name: 'Pantry Staples',
        slug: 'pantry-staples',
        description: 'Rice, pasta, canned goods, and cooking essentials',
      },
      {
        name: 'Gourmet & Specialty',
        slug: 'gourmet-specialty',
        description: 'Premium and specialty food items',
      },
    ],
  },
];

// Helper function to create rich text content
function createRichTextContent(text) {
  return [
    {
      type: 'paragraph',
      children: [
        {
          type: 'text',
          text: text,
        },
      ],
    },
  ];
}

// Helper function to generate product data for a category
function generateProductsForCategory(categorySlug, categoryName, count = 12) {
  const products = [];
  const productTemplates = {
    electronics: [
      {
        name: 'Premium Wireless Headphones',
        basePrice: 299.99,
        description: 'High-quality wireless headphones with noise cancellation',
      },
      {
        name: 'Smart Fitness Watch',
        basePrice: 199.99,
        description: 'Advanced fitness tracking with heart rate monitor',
      },
      {
        name: 'Professional Camera Lens',
        basePrice: 599.99,
        description: 'Professional-grade camera lens for photography enthusiasts',
      },
      {
        name: 'Gaming Mechanical Keyboard',
        basePrice: 149.99,
        description: 'RGB mechanical keyboard for gaming and productivity',
      },
      {
        name: 'Wireless Charging Pad',
        basePrice: 49.99,
        description: 'Fast wireless charging for compatible devices',
      },
      {
        name: '4K Webcam',
        basePrice: 129.99,
        description: 'Ultra HD webcam for streaming and video calls',
      },
      {
        name: 'Bluetooth Speaker',
        basePrice: 79.99,
        description: 'Portable Bluetooth speaker with premium sound quality',
      },
      {
        name: 'USB-C Hub',
        basePrice: 89.99,
        description: 'Multi-port USB-C hub with HDMI and USB 3.0',
      },
      {
        name: 'Wireless Mouse',
        basePrice: 59.99,
        description: 'Ergonomic wireless mouse with precision tracking',
      },
      {
        name: 'Phone Case with MagSafe',
        basePrice: 39.99,
        description: 'Protective case with magnetic wireless charging support',
      },
      {
        name: 'Tablet Stand',
        basePrice: 29.99,
        description: 'Adjustable stand for tablets and smartphones',
      },
      {
        name: 'Power Bank 20000mAh',
        basePrice: 69.99,
        description: 'High-capacity portable charger with fast charging',
      },
    ],
    fashion: [
      {
        name: 'Organic Cotton T-Shirt',
        basePrice: 29.99,
        description: 'Comfortable organic cotton t-shirt in various colors',
      },
      {
        name: 'Designer Denim Jeans',
        basePrice: 89.99,
        description: 'Premium denim jeans with modern fit',
      },
      {
        name: 'Casual Summer Dress',
        basePrice: 59.99,
        description: 'Light and breezy summer dress for casual occasions',
      },
      {
        name: 'Leather Crossbody Bag',
        basePrice: 79.99,
        description: 'Genuine leather crossbody bag with adjustable strap',
      },
      {
        name: 'Running Sneakers',
        basePrice: 119.99,
        description: 'Lightweight running shoes with cushioned sole',
      },
      {
        name: 'Wool Blend Sweater',
        basePrice: 69.99,
        description: 'Cozy wool blend sweater for cooler weather',
      },
      {
        name: 'Silk Scarf',
        basePrice: 49.99,
        description: 'Elegant silk scarf with artistic print',
      },
      {
        name: 'Leather Belt',
        basePrice: 39.99,
        description: 'Classic leather belt with metal buckle',
      },
      {
        name: 'Cotton Pajama Set',
        basePrice: 45.99,
        description: 'Comfortable cotton pajama set for better sleep',
      },
      {
        name: 'Denim Jacket',
        basePrice: 79.99,
        description: 'Classic denim jacket with vintage wash',
      },
      {
        name: 'Athletic Shorts',
        basePrice: 34.99,
        description: 'Moisture-wicking athletic shorts for workouts',
      },
      {
        name: 'Formal Dress Shirt',
        basePrice: 54.99,
        description: 'Crisp formal dress shirt for business occasions',
      },
    ],
    'home-garden': [
      {
        name: 'Premium Coffee Maker',
        basePrice: 199.99,
        description: 'Professional-grade coffee maker with programmable features',
      },
      {
        name: 'Ergonomic Office Chair',
        basePrice: 299.99,
        description: 'Comfortable office chair with lumbar support',
      },
      {
        name: 'LED Desk Lamp',
        basePrice: 49.99,
        description: 'Adjustable LED desk lamp with multiple brightness levels',
      },
      {
        name: 'Bamboo Cutting Board Set',
        basePrice: 34.99,
        description: 'Eco-friendly bamboo cutting boards in various sizes',
      },
      {
        name: 'Ceramic Dinnerware Set',
        basePrice: 89.99,
        description: 'Complete dinnerware set for 4 people',
      },
      {
        name: 'Memory Foam Pillow',
        basePrice: 59.99,
        description: 'Contoured memory foam pillow for better sleep',
      },
      {
        name: 'Indoor Plant Collection',
        basePrice: 79.99,
        description: 'Set of 3 low-maintenance indoor plants',
      },
      {
        name: 'Storage Ottoman',
        basePrice: 69.99,
        description: 'Multi-functional storage ottoman with cushioned top',
      },
      {
        name: 'Wall Art Canvas Set',
        basePrice: 89.99,
        description: 'Modern abstract wall art canvas set of 3',
      },
      {
        name: 'Essential Oil Diffuser',
        basePrice: 45.99,
        description: 'Ultrasonic essential oil diffuser with LED lights',
      },
      {
        name: 'Throw Blanket',
        basePrice: 39.99,
        description: 'Soft and cozy throw blanket for living room',
      },
      {
        name: 'Kitchen Scale',
        basePrice: 29.99,
        description: 'Digital kitchen scale with precise measurements',
      },
    ],
  };

  // Default template for categories not specifically defined
  const defaultTemplate = [
    {
      name: `Premium ${categoryName} Item 1`,
      basePrice: 99.99,
      description: `High-quality ${categoryName.toLowerCase()} product`,
    },
    {
      name: `Professional ${categoryName} Tool`,
      basePrice: 149.99,
      description: `Professional-grade ${categoryName.toLowerCase()} equipment`,
    },
    {
      name: `Eco-Friendly ${categoryName} Product`,
      basePrice: 79.99,
      description: `Sustainable ${categoryName.toLowerCase()} option`,
    },
    {
      name: `Luxury ${categoryName} Collection`,
      basePrice: 199.99,
      description: `Premium ${categoryName.toLowerCase()} collection item`,
    },
  ];

  const templates = productTemplates[categorySlug] || defaultTemplate;

  for (let i = 0; i < count; i++) {
    const template = templates[i % templates.length];
    const variation = Math.floor(i / templates.length) + 1;

    const productName = variation > 1 ? `${template.name} v${variation}` : template.name;
    const baseSlug = template.name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

    const product = {
      name: productName,
      slug: `${baseSlug}-${categorySlug}-${String(i + 1).padStart(3, '0')}`,
      description: createRichTextContent(template.description),
      short_description: template.description,
      price: Math.round((template.basePrice + (Math.random() * 50 - 25)) * 100) / 100, // Add some price variation
      sale_price: Math.random() > 0.7 ? Math.round(template.basePrice * 0.8 * 100) / 100 : null, // 30% chance of sale
      sku: `${categorySlug.toUpperCase()}-${String(i + 1).padStart(3, '0')}`,
      inventory_quantity: Math.floor(Math.random() * 100) + 10,
      product_status: 'Published',
      featured: Math.random() > 0.8, // 20% chance of being featured
      tags: `${categoryName}, Premium, Quality`,
      weight: Math.round((Math.random() * 5 + 0.1) * 100) / 100,
      categories: [], // Will be populated with category IDs later
    };

    products.push(product);
  }

  return products;
}

// Configure API permissions for public access
async function configureAPIPermissions() {
  console.log('🔧 Configuring API permissions...');

  try {
    // Configure permissions for product-categories
    await configureContentTypePermissions('product-category');

    // Configure permissions for products
    await configureContentTypePermissions('product');

    console.log('✅ API permissions configured successfully');
  } catch (error) {
    console.error('❌ Error configuring permissions:', error.message);
    // Continue execution even if permissions fail
  }
}

// Configure permissions for a specific content type
async function configureContentTypePermissions(contentType) {
  try {
    const permissionsData = {
      permissions: [
        {
          action: `api::${contentType}.${contentType}.find`,
          subject: null,
          properties: {},
          conditions: [],
        },
        {
          action: `api::${contentType}.${contentType}.findOne`,
          subject: null,
          properties: {},
          conditions: [],
        },
      ],
    };

    // This would typically require admin authentication
    // For now, we'll skip this and handle permissions manually in admin panel
    console.log(`📝 Permissions configuration prepared for ${contentType}`);
  } catch (error) {
    console.warn(`⚠️ Could not configure permissions for ${contentType}:`, error.message);
  }
}

// Main execution function
async function populateComprehensiveData() {
  console.log('🚀 Starting comprehensive Strapi CMS data population...');

  try {
    // Step 1: Configure API permissions
    await configureAPIPermissions();

    // Step 2: Populate categories
    const categoryIds = await populateCategories();

    // Step 3: Populate products
    await populateProducts(categoryIds);

    // Step 4: Verify data
    await verifyData();

    console.log('✅ Comprehensive data population completed successfully!');
  } catch (error) {
    console.error('❌ Error during data population:', error.message);
    process.exit(1);
  }
}

// Populate categories in Strapi
async function populateCategories() {
  console.log('📂 Populating categories...');
  const categoryIds = new Map();

  try {
    for (const categoryData of categoriesData) {
      console.log(`📝 Creating category: ${categoryData.name}`);

      // Create main category
      const mainCategory = {
        data: {
          name: categoryData.name,
          slug: categoryData.slug,
          description: createRichTextContent(categoryData.description),
          featured: categoryData.featured,
          parent: null,
        },
      };

      try {
        const response = await publicAPI.post('/product-categories', mainCategory);
        const mainCategoryId = response.data.data.id;
        categoryIds.set(categoryData.slug, mainCategoryId);
        console.log(`✅ Created main category: ${categoryData.name} (ID: ${mainCategoryId})`);

        // Create subcategories
        if (categoryData.subcategories) {
          for (const subcat of categoryData.subcategories) {
            const subcategory = {
              data: {
                name: subcat.name,
                slug: subcat.slug,
                description: createRichTextContent(subcat.description),
                featured: false,
                parent: mainCategoryId,
              },
            };

            try {
              const subResponse = await publicAPI.post('/product-categories', subcategory);
              const subcategoryId = subResponse.data.data.id;
              categoryIds.set(subcat.slug, subcategoryId);
              console.log(`  ✅ Created subcategory: ${subcat.name} (ID: ${subcategoryId})`);
            } catch (subError) {
              console.warn(
                `  ⚠️ Could not create subcategory ${subcat.name}:`,
                subError.response?.data?.error?.message || subError.message
              );
            }
          }
        }

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.warn(
          `⚠️ Could not create category ${categoryData.name}:`,
          error.response?.data?.error?.message || error.message
        );
      }
    }

    console.log(`✅ Categories population completed. Created ${categoryIds.size} categories.`);
    return categoryIds;
  } catch (error) {
    console.error('❌ Error populating categories:', error.message);
    throw error;
  }
}

// Execute the script
if (require.main === module) {
  populateComprehensiveData();
}

// Populate products in Strapi
async function populateProducts(categoryIds) {
  console.log('🛍️ Populating products...');
  let totalProducts = 0;

  try {
    for (const categoryData of categoriesData) {
      console.log(`📦 Creating products for category: ${categoryData.name}`);

      const categoryId = categoryIds.get(categoryData.slug);
      if (!categoryId) {
        console.warn(`⚠️ Category ID not found for ${categoryData.slug}, skipping products`);
        continue;
      }

      const products = generateProductsForCategory(categoryData.slug, categoryData.name, 12);

      for (const productData of products) {
        try {
          const product = {
            data: {
              ...productData,
              categories: [categoryId], // Associate with main category
            },
          };

          const response = await publicAPI.post('/products', product);
          totalProducts++;
          console.log(`  ✅ Created product: ${productData.name} (ID: ${response.data.data.id})`);

          // Add delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 50));
        } catch (error) {
          console.warn(
            `  ⚠️ Could not create product ${productData.name}:`,
            error.response?.data?.error?.message || error.message
          );
        }
      }
    }

    console.log(`✅ Products population completed. Created ${totalProducts} products.`);
  } catch (error) {
    console.error('❌ Error populating products:', error.message);
    throw error;
  }
}

// Verify populated data
async function verifyData() {
  console.log('🔍 Verifying populated data...');

  try {
    // Verify categories
    const categoriesResponse = await publicAPI.get('/product-categories?populate=*');
    const categoriesCount = categoriesResponse.data.data.length;
    console.log(`📂 Found ${categoriesCount} categories in Strapi`);

    // Verify products
    const productsResponse = await publicAPI.get('/products?populate=*');
    const productsCount = productsResponse.data.data.length;
    console.log(`🛍️ Found ${productsCount} products in Strapi`);

    // Verify featured categories
    const featuredCategoriesResponse = await publicAPI.get(
      '/product-categories?filters[featured][$eq]=true'
    );
    const featuredCount = featuredCategoriesResponse.data.data.length;
    console.log(`⭐ Found ${featuredCount} featured categories`);

    console.log('✅ Data verification completed successfully');

    return {
      categories: categoriesCount,
      products: productsCount,
      featuredCategories: featuredCount,
    };
  } catch (error) {
    console.error(
      '❌ Error verifying data:',
      error.response?.data?.error?.message || error.message
    );
    throw error;
  }
}

module.exports = {
  populateComprehensiveData,
  categoriesData,
  createRichTextContent,
  generateProductsForCategory,
  populateCategories,
  populateProducts,
  verifyData,
};
